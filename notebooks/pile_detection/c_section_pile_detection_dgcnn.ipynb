{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# C-Section Pile Detection (DGCNN)\n", "\n", "This notebook implements deep learning-based C-section pile detection as part of the pile detection stage. It uses DGCNN (Dynamic Graph CNN) to process ground-filtered or aligned point clouds and detect C-section pile structures.\n", "\n", "**Stage**: <PERSON>le Detection  \n", "**Input Data**: Ground-filtered or aligned point cloud  \n", "**Output**: Pile center coordinates + types (C-section, cylindrical, etc.)  \n", "**Format**: .csv (columns: x, y, z, pile_type, confidence, etc.)  \n", "**Model**: DGCNN for point-wise classification  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Process Overview:\n", "1. **Load Ground-Filtered Data**: Import processed point cloud from ground segmentation or alignment\n", "2. **Patch Generation**: Create overlapping 3D patches for DGCNN processing\n", "3. **C-Section Detection**: Apply DGCNN model for point-wise classification\n", "4. **Post-Processing**: Cluster detected points and extract pile centers\n", "5. **Export Results**: Save detected pile data in .csv format"]}, {"cell_type": "markdown", "metadata": {"tags": ["parameters"]}, "source": ["## Papermill Parameters\n", "\n", "These parameters can be overridden when running with Papermill for batch processing."]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - can be overridden during execution\n", "site_name = \"site_001\"\n", "ground_method = \"csf\"  # Options: csf, pmf, ransac\n", "confidence_threshold = 0.7\n", "model_path = \"../../models/dgcnn_csection_pile.pth\"\n", "input_data_dir = \"../../data/processed/ground_segmentation\"\n", "output_dir = \"../../output_runs/pile_detection\"\n", "mlflow_experiment_name = \"pile_detection_dgcnn\"\n", "mlflow_run_name = f\"c_section_{site_name}_{ground_method}\"\n", "enable_cross_validation = True\n", "cv_folds = 5\n", "enable_enhanced_analysis = True"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: open3d in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (0.19.0)\n", "Requirement already satisfied: laspy in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (2.5.4)\n", "Requirement already satisfied: numpy>=1.18.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (1.26.4)\n", "Requirement already satisfied: dash>=2.6.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (3.0.4)\n", "Requirement already satisfied: werkzeug>=3.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (3.0.6)\n", "Requirement already satisfied: flask>=3.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (3.0.3)\n", "Requirement already satisfied: nbformat>=5.7.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from open3d) (5.10.4)\n", "Requirement already satisfied: configargparse in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (1.7.1)\n", "Requirement already satisfied: addict in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (2.4.0)\n", "Requirement already satisfied: pillow>=9.3.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (11.2.1)\n", "Requirement already satisfied: matplotlib>=3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (3.10.3)\n", "Requirement already satisfied: pandas>=1.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (2.3.0)\n", "Requirement already satisfied: pyyaml>=5.4.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (6.0.2)\n", "Requirement already satisfied: scikit-learn>=0.21 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (1.7.0)\n", "Requirement already satisfied: tqdm in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (4.67.1)\n", "Requirement already satisfied: pyquaternion in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (0.9.9)\n", "Requirement already satisfied: plotly>=5.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from dash>=2.6.0->open3d) (6.1.2)\n", "Requirement already satisfied: importlib-metadata in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from dash>=2.6.0->open3d) (8.7.0)\n", "Requirement already satisfied: typing-extensions>=4.1.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from dash>=2.6.0->open3d) (4.14.0)\n", "Requirement already satisfied: requests in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from dash>=2.6.0->open3d) (2.32.4)\n", "Requirement already satisfied: retrying in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from dash>=2.6.0->open3d) (1.3.5)\n", "Requirement already satisfied: nest-asyncio in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from dash>=2.6.0->open3d) (1.6.0)\n", "Requirement already satisfied: setuptools in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from dash>=2.6.0->open3d) (80.9.0)\n", "Requirement already satisfied: Jinja2>=3.1.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from flask>=3.0.0->open3d) (3.1.6)\n", "Requirement already satisfied: itsdangerous>=2.1.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from flask>=3.0.0->open3d) (2.2.0)\n", "Requirement already satisfied: click>=8.1.3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from flask>=3.0.0->open3d) (8.2.1)\n", "Requirement already satisfied: blinker>=1.6.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from flask>=3.0.0->open3d) (1.9.0)\n", "Requirement already satisfied: MarkupSafe>=2.1.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from werkzeug>=3.0.0->open3d) (3.0.2)\n", "Requirement already satisfied: contourpy>=1.0.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib>=3->open3d) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib>=3->open3d) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib>=3->open3d) (4.58.4)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib>=3->open3d) (1.4.8)\n", "Requirement already satisfied: packaging>=20.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib>=3->open3d) (25.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib>=3->open3d) (3.2.3)\n", "Requirement already satisfied: python-dateutil>=2.7 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib>=3->open3d) (2.9.0.post0)\n", "Requirement already satisfied: fastjsonschema>=2.15 in /Users/<USER>/.local/lib/python3.11/site-packages (from nbformat>=5.7.0->open3d) (2.21.1)\n", "Requirement already satisfied: jsonschema>=2.6 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from nbformat>=5.7.0->open3d) (4.19.0)\n", "Requirement already satisfied: jupyter-core!=5.0.*,>=4.12 in /Users/<USER>/.local/lib/python3.11/site-packages (from nbformat>=5.7.0->open3d) (5.7.2)\n", "Requirement already satisfied: traitlets>=5.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from nbformat>=5.7.0->open3d) (5.14.3)\n", "Requirement already satisfied: attrs>=22.2.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (25.3.0)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (2025.4.1)\n", "Requirement already satisfied: referencing>=0.28.4 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (0.36.2)\n", "Requirement already satisfied: rpds-py>=0.7.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (0.25.1)\n", "Requirement already satisfied: platformdirs>=2.5 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from jupyter-core!=5.0.*,>=4.12->nbformat>=5.7.0->open3d) (4.3.8)\n", "Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pandas>=1.0->open3d) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pandas>=1.0->open3d) (2025.2)\n", "Requirement already satisfied: narwhals>=1.15.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from plotly>=5.0.0->dash>=2.6.0->open3d) (1.44.0)\n", "Requirement already satisfied: six>=1.5 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from python-dateutil>=2.7->matplotlib>=3->open3d) (1.17.0)\n", "Requirement already satisfied: scipy>=1.8.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from scikit-learn>=0.21->open3d) (1.16.0)\n", "Requirement already satisfied: joblib>=1.2.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from scikit-learn>=0.21->open3d) (1.5.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from scikit-learn>=0.21->open3d) (3.6.0)\n", "Requirement already satisfied: zipp>=3.20 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from importlib-metadata->dash>=2.6.0->open3d) (3.23.0)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from requests->dash>=2.6.0->open3d) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from requests->dash>=2.6.0->open3d) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from requests->dash>=2.6.0->open3d) (2.5.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from requests->dash>=2.6.0->open3d) (2025.6.15)\n"]}], "source": ["# Install required packages\n", "#!pip install torch torchvision torch-geometric torch-points3d open3d matplotlib numpy scipy pandas\n", "#!pip install scikit-learn plotly\n", "!python -m pip install open3d laspy"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PyTorch version: 2.5.1\n", "CUDA available: False\n", "Using device: cpu\n"]}], "source": ["# Import libraries\n", "import os\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import open3d as o3d\n", "from sklearn.neighbors import NearestNeighbors\n", "from sklearn.cluster import DBSCAN\n", "from scipy.spatial import ConvexHull\n", "from scipy.spatial.distance import cdist\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "\n", "#from torch.utils.data import Dataset, DataLoader\n", "from torch_geometric.loader import DataLoader  \n", "\n", "from torch_geometric.nn import DynamicEdgeConv, global_max_pool\n", "from torch_geometric.data import Data, Batch\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. C-<PERSON>le Characteristics\n", "\n", "Define the geometric characteristics of C-section piles for detection."]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["C-Section Pile Detection Configuration:\n", "Flange width range: (0.05, 0.3) m\n", "Web height range: (0.1, 0.8) m\n", "Opening width range: (0.08, 0.25) m\n", "Thickness range: (0.005, 0.03) m\n", "Patch size: 2.0 m\n"]}], "source": ["class CSectionPileConfig:\n", "    \"\"\"\n", "    Configuration for C-section pile detection.\n", "    \"\"\"\n", "    def __init__(self):\n", "        # C-section dimensions (typical ranges)\n", "        self.flange_width_range = (0.05, 0.3)  # meters\n", "        self.web_height_range = (0.1, 0.8)  # meters\n", "        self.thickness_range = (0.005, 0.03)  # meters\n", "        self.opening_width_range = (0.08, 0.25)  # meters (gap between flanges)\n", "        self.height_range = (0.1, 1.0)  # meters\n", "        \n", "        # Detection parameters\n", "        self.patch_size = 2.0  # meters\n", "        self.min_points_per_patch = 100\n", "        self.overlap_ratio = 0.5\n", "        \n", "        # DGCNN parameters\n", "        self.k_neighbors = 20\n", "        self.num_points = 1024\n", "        self.feature_dims = [64, 128, 256, 512]\n", "        self.num_classes = 2  # pile vs non-pile\n", "\n", "config = CSectionPileConfig()\n", "print(\"C-Section Pile Detection Configuration:\")\n", "print(f\"Flange width range: {config.flange_width_range} m\")\n", "print(f\"Web height range: {config.web_height_range} m\")\n", "print(f\"Opening width range: {config.opening_width_range} m\")\n", "print(f\"Thickness range: {config.thickness_range} m\")\n", "print(f\"Patch size: {config.patch_size} m\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Point Cloud Patch Generation\n", "\n", "Generate patches from point clouds for training and inference."]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["def generate_patches_from_point_cloud(points, colors=None, patch_size=2.0, overlap_ratio=0.5, min_points=100):\n", "    \"\"\"\n", "    Generate overlapping patches from a point cloud.\n", "    \"\"\"\n", "    # Calculate bounds\n", "    min_coords = points.min(axis=0)\n", "    max_coords = points.max(axis=0)\n", "    \n", "    # Calculate step size\n", "    step_size = patch_size * (1 - overlap_ratio)\n", "    \n", "    patches = []\n", "    patch_id = 0\n", "    \n", "    # Generate grid of patch centers\n", "    x_centers = np.arange(min_coords[0], max_coords[0], step_size)\n", "    y_centers = np.arange(min_coords[1], max_coords[1], step_size)\n", "    \n", "    for x_center in x_centers:\n", "        for y_center in y_centers:\n", "            # Define patch bounds\n", "            x_min = x_center - patch_size / 2\n", "            x_max = x_center + patch_size / 2\n", "            y_min = y_center - patch_size / 2\n", "            y_max = y_center + patch_size / 2\n", "            \n", "            # Find points within patch\n", "            mask = ((points[:, 0] >= x_min) & (points[:, 0] <= x_max) &\n", "                   (points[:, 1] >= y_min) & (points[:, 1] <= y_max))\n", "            \n", "            patch_points = points[mask]\n", "            \n", "            if len(patch_points) >= min_points:\n", "                # Center the patch points\n", "                patch_center = np.array([x_center, y_center, patch_points[:, 2].mean()])\n", "                centered_points = patch_points - patch_center\n", "                \n", "                patch_data = {\n", "                    'id': patch_id,\n", "                    'points': centered_points,\n", "                    'original_points': patch_points,\n", "                    'center': patch_center,\n", "                    'bounds': (x_min, y_min, x_max, y_max),\n", "                    'num_points': len(patch_points)\n", "                }\n", "                \n", "                if colors is not None:\n", "                    patch_data['colors'] = colors[mask]\n", "                \n", "                patches.append(patch_data)\n", "                patch_id += 1\n", "    \n", "    print(f\"Generated {len(patches)} patches from point cloud\")\n", "    return patches"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. C-Section Pile Geometric Analysis\n", "\n", "Analyze point cloud patches for C-section pile characteristics."]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["def analyze_c_section_geometry(points, tolerance=0.05):\n", "    \"\"\"\n", "    Analyze point cloud patch for C-section pile geometry.    \n", "    \"\"\"\n", "    if len(points) < 10:\n", "        return None\n", "    \n", "    # Project points to XY plane for cross-section analysis\n", "    xy_points = points[:, :2]\n", "    \n", "    # Find convex hull\n", "    try:\n", "        hull = ConvexHull(xy_points)\n", "        hull_points = xy_points[hull.vertices]\n", "    except:\n", "        return None\n", "    \n", "    # Calculate bounding box\n", "    min_coords = points.min(axis=0)\n", "    max_coords = points.max(axis=0)\n", "    dimensions = max_coords - min_coords\n", "    \n", "    # Analyze cross-sectional shape for C-section\n", "    # For C-section, we expect:\n", "    # 1. U-shaped or C-shaped cross-section\n", "    # 2. Two flanges connected by a web\n", "    # 3. Opening on one side\n", "    # 4. Specific width-to-height ratios\n", "    \n", "    # Calculate aspect ratios\n", "    xy_aspect = dimensions[0] / dimensions[1] if dimensions[1] > 0 else 0\n", "    if xy_aspect < 1:\n", "        xy_aspect = 1 / xy_aspect\n", "    \n", "    # Analyze point distribution for C-section pattern\n", "    # Cluster points in cross-section\n", "    clustering = DBSCAN(eps=tolerance, min_samples=5)\n", "    cluster_labels = clustering.fit_predict(xy_points)\n", "    \n", "    unique_labels = np.unique(cluster_labels)\n", "    num_clusters = len(unique_labels[unique_labels >= 0])  # Exclude noise (-1)\n", "    \n", "    # Calculate density distribution for C-shape detection\n", "    center_x, center_y = xy_points.mean(axis=0)\n", "    \n", "    # Divide into radial sectors to detect C-shape opening\n", "    angles = np.arctan2(xy_points[:, 1] - center_y, xy_points[:, 0] - center_x)\n", "    angle_bins = np.linspace(-np.pi, np.pi, 16)\n", "    angle_hist, _ = np.histogram(angles, bins=angle_bins)\n", "    \n", "    # Find potential opening (sector with low point density)\n", "    min_density_sector = np.argmin(angle_hist)\n", "    opening_ratio = angle_hist[min_density_sector] / (angle_hist.max() + 1e-6)\n", "    \n", "    # Calculate concavity measure\n", "    # C-sections should have higher concavity than solid shapes\n", "    hull_area = hull.volume if hasattr(hull, 'volume') else 0\n", "    point_area = len(points) * tolerance * tolerance  # Approximate area\n", "    concavity = 1 - (point_area / (hull_area + 1e-6))\n", "    \n", "    # Analyze symmetry (C-sections often have some symmetry)\n", "    # Check for reflection symmetry across different axes\n", "    reflected_x = np.column_stack([-xy_points[:, 0], xy_points[:, 1]])\n", "    reflected_y = np.column_stack([xy_points[:, 0], -xy_points[:, 1]])\n", "    \n", "    # Calculate symmetry scores (simplified)\n", "    x_symmetry = np.mean(np.min(cdist(xy_points, reflected_x), axis=1))\n", "    y_symmetry = np.mean(np.min(cdist(xy_points, reflected_y), axis=1))\n", "    \n", "    # Calculate features\n", "    features = {\n", "        'num_points': len(points),\n", "        'dimensions': dimensions,\n", "        'xy_aspect_ratio': xy_aspect,\n", "        'height': dimensions[2],\n", "        'width': max(dimensions[0], dimensions[1]),\n", "        'thickness': min(dimensions[0], dimensions[1]),\n", "        'num_clusters': num_clusters,\n", "        'hull_area': hull_area,\n", "        'opening_ratio': opening_ratio,\n", "        'concavity': concavity,\n", "        'x_symmetry': x_symmetry,\n", "        'y_symmetry': y_symmetry,\n", "        'compactness': len(points) / hull_area if hull_area > 0 else 0,\n", "        'c_shape_score': opening_ratio * concavity * (1 - min(x_symmetry, y_symmetry))\n", "    }\n", "    \n", "    return features"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. DGCNN Model for C-Section Pile Detection\n", "\n", "Implement Dynamic Graph CNN for point-wise classification."]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["class DGCNNCSectionPile(nn.Module):\n", "    \"\"\"\n", "    DGCNN model for C-section pile detection.\n", "    \"\"\"\n", "    def __init__(self, k=20, feature_dims=[64, 128, 256, 512], num_classes=2, dropout=0.5):\n", "        super(DGCNNCSectionPile, self).__init__()\n", "        self.k = k\n", "        self.num_classes = num_classes\n", "        \n", "        # Edge convolution layers\n", "        self.conv1 = DynamicEdgeConv(nn.Sequential(\n", "            nn.Linear(6, feature_dims[0]),  # 6 = 3 (xyz) * 2 (point and neighbor)\n", "            nn.BatchNorm1d(feature_dims[0]),\n", "            nn.ReLU(),\n", "            nn.Linear(feature_dims[0], feature_dims[0])\n", "        ), k=k, aggr='max')\n", "        \n", "        self.conv2 = DynamicEdgeConv(nn.Sequential(\n", "            nn.Linear(feature_dims[0] * 2, feature_dims[1]),\n", "            nn.BatchNorm1d(feature_dims[1]),\n", "            nn.ReLU(),\n", "            nn.Linear(feature_dims[1], feature_dims[1])\n", "        ), k=k, aggr='max')\n", "        \n", "        self.conv3 = DynamicEdgeConv(nn.Sequential(\n", "            nn.Linear(feature_dims[1] * 2, feature_dims[2]),\n", "            nn.BatchNorm1d(feature_dims[2]),\n", "            nn.ReLU(),\n", "            nn.Linear(feature_dims[2], feature_dims[2])\n", "        ), k=k, aggr='max')\n", "        \n", "        self.conv4 = DynamicEdgeConv(nn.Sequential(\n", "            nn.Linear(feature_dims[2] * 2, feature_dims[3]),\n", "            nn.BatchNorm1d(feature_dims[3]),\n", "            nn.ReLU(),\n", "            nn.Linear(feature_dims[3], feature_dims[3])\n", "        ), k=k, aggr='max')\n", "        \n", "        # Global feature aggregation\n", "        total_features = sum(feature_dims)\n", "        \n", "        # Classification head\n", "        self.classifier = nn.Sequential(\n", "            nn.Linear(total_features, 512),\n", "            nn.BatchNorm1d(512),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.<PERSON><PERSON>(512, 256),\n", "            nn.BatchNorm1d(256),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(256, num_classes)\n", "        )\n", "        \n", "        # Point-wise classification head\n", "        self.point_classifier = nn.Sequential(\n", "            nn.Linear(total_features, 256),\n", "            nn.BatchNorm1d(256),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.<PERSON>(256, 128),\n", "            nn.<PERSON>chNorm1d(128),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(128, num_classes)\n", "        )\n", "    \n", "    def forward(self, data):\n", "        x, batch = data.x, data.batch\n", "        \n", "        # Extract features through edge convolutions\n", "        x1 = self.conv1(x, batch)\n", "        x2 = self.conv2(x1, batch)\n", "        x3 = self.conv3(x2, batch)\n", "        x4 = self.conv4(x3, batch)\n", "        \n", "        # Concatenate all features\n", "        x_concat = torch.cat([x1, x2, x3, x4], dim=1)\n", "        \n", "        # Global classification (patch-level)\n", "        global_features = global_max_pool(x_concat, batch)\n", "        global_pred = self.classifier(global_features)\n", "        \n", "        # Point-wise classification\n", "        point_pred = self.point_classifier(x_concat)\n", "        \n", "        return global_pred, point_pred"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Ground-Filtered Point Cloud Loading\n", "\n", "Load processed point cloud data from ground segmentation or alignment stage."]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing site: castro\n", "Ground segmentation method: ransac\n", "Confidence threshold: 0.3\n"]}], "source": ["# Configuration for data paths\n", "import pandas as pd\n", "import laspy\n", "\n", "# Define input and output paths\n", "input_data_dir = Path(\"../../data/processed/ground_segmentation\")\n", "output_dir = Path(\"../../output_runs/pile_detection\")\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Parameters for Papermill execution\n", "site_name = \"castro\"  # Will be parameterized\n", "ground_method = \"ransac\"   # Options: csf, pmf, ransac\n", "confidence_threshold = 0.3\n", "model_path = \"../../models/dgcnn_csection_pile.pth\"\n", "\n", "print(f\"Processing site: {site_name}\")\n", "print(f\"Ground segmentation method: {ground_method}\")\n", "print(f\"Confidence threshold: {confidence_threshold}\")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["def load_ground_filtered_point_cloud(site_name=None, method=\"csf\",input_path=None):\n", "    \"\"\"\n", "    Load ground-filtered point cloud from previous processing stage.\n", "    \"\"\"\n", "    # Look for ground-filtered files\n", "    if input_path:\n", "        las_file = Path(input_path)\n", "        if not las_file.exists():\n", "            raise FileNotFoundError(f\"File not found: {las_file}\")\n", "    else:\n", "        if not site_name:\n", "            raise ValueError(\"site_name must be provided if input_path is not specified.\")\n", "\n", "        las_file = input_data_dir / f\"{site_name}_ground_filtered_{method}.las\"\n", "    \n", "        if not las_file.exists():\n", "            # Try alternative naming conventions\n", "            alternative_files = list(input_data_dir.glob(f\"{site_name}*{method}*.las\"))\n", "            if alternative_files:\n", "                las_file = alternative_files[0]\n", "            else:\n", "                raise FileNotFoundError(f\"No ground-filtered LAS file found for {site_name} with method {method}\")\n", "    \n", "    print(f\"Loading point cloud from: {las_file}\")\n", "    \n", "    ext = las_file.suffix.lower()\n", "\n", "    if ext == \".las\" or ext == \".laz\":\n", "        # Load LAS file\n", "        las = laspy.read(str(las_file))\n", "\n", "        # Extract coordinates\n", "        points = np.vstack([las.x, las.y, las.z]).transpose()\n", "        \n", "        # Extract colors if available\n", "        colors = None\n", "        if hasattr(las, 'red') and hasattr(las, 'green') and hasattr(las, 'blue'):\n", "            colors = np.vstack([las.red, las.green, las.blue]).transpose()\n", "            colors = colors / 65535.0  # Normalize to [0, 1]\n", "\n", "    elif ext == \".ply\":\n", "        import open3d as o3d\n", "        pcd = o3d.io.read_point_cloud(str(las_file))\n", "        points = np.asarray(pcd.points)\n", "        colors = np.asarray(pcd.colors) if pcd.has_colors() else None\n", "\n", "    else:\n", "        raise ValueError(f\"Unsupported file extension: {ext}\")\n", "\n", "    \n", "    # Extract metadata\n", "    metadata = {\n", "        'num_points': len(points),\n", "        'bounds': {\n", "            'x_min': points[:, 0].min(), 'x_max': points[:, 0].max(),\n", "            'y_min': points[:, 1].min(), 'y_max': points[:, 1].max(),\n", "            'z_min': points[:, 2].min(), 'z_max': points[:, 2].max()\n", "        },\n", "        'file_path': str(las_file),\n", "        'ground_method': method\n", "    }\n", "    \n", "    print(f\"Loaded {len(points):,} points\")\n", "    print(f\"Bounds: X[{metadata['bounds']['x_min']:.2f}, {metadata['bounds']['x_max']:.2f}] \")\n", "    print(f\"        Y[{metadata['bounds']['y_min']:.2f}, {metadata['bounds']['y_max']:.2f}] \")\n", "    print(f\"        Z[{metadata['bounds']['z_min']:.2f}, {metadata['bounds']['z_max']:.2f}]\")\n", "    \n", "    return points, colors, metadata"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading point cloud from: ../preprocessing/ground_segmentation/output_runs/Castro_ransac_pmf_20250623_185602/ransac_only_nonground.ply\n", "Loaded 644,537 points\n", "Bounds: X[707251.78, 707840.48] \n", "        Y[4692831.50, 4693173.90] \n", "        Z[47.91, 71.26]\n", "Point Cloud Statistics for Site: castro (Method: ransac)\n", "Total points: 644,537\n", "Point density: 3.20 points/m²\n"]}], "source": ["# Load the ground-filtered point cloud using an explicit .ply file path\n", "input_ply_path = \"../preprocessing/ground_segmentation/output_runs/Castro_ransac_pmf_20250623_185602/ransac_only_nonground.ply\"\n", "points, colors, metadata = load_ground_filtered_point_cloud(input_path=input_ply_path)\n", "\n", "\n", "# Display basic statistics\n", "print(f\"Point Cloud Statistics for Site: {site_name} (Method: {ground_method})\")\n", "print(f\"Total points: {len(points):,}\")\n", "\n", "# Calculate area from bounds\n", "x_range = metadata['bounds']['x_max'] - metadata['bounds']['x_min']\n", "y_range = metadata['bounds']['y_max'] - metadata['bounds']['y_min']\n", "area_m2 = x_range * y_range\n", "\n", "# Safeguard against division by zero\n", "if area_m2 > 0:\n", "    point_density = len(points) / area_m2\n", "    print(f\"Point density: {point_density:.2f} points/m²\")\n", "else:\n", "    print(\"Invalid bounding box dimensions — cannot compute point density.\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Data Preprocessing for DGCNN Input\n", "\n", "Prepare point cloud patches for DGCNN model input."]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["from torch.utils.data import Dataset\n", "\n", "class PointCloudDataset(Dataset):\n", "    \"\"\"\n", "    Dataset class for point cloud patches.\n", "    \"\"\"\n", "    def __init__(self, patches, num_points=1024, augment=False):\n", "        self.patches = patches\n", "        self.num_points = num_points\n", "        self.augment = augment\n", "    \n", "    def __len__(self):\n", "        return len(self.patches)\n", "    \n", "    def __getitem__(self, idx):\n", "        patch = self.patches[idx]\n", "        points = patch['points']\n", "        \n", "        # Sample or pad points to fixed size\n", "        if len(points) >= self.num_points:\n", "            # Random sampling\n", "            indices = np.random.choice(len(points), self.num_points, replace=False)\n", "            points = points[indices]\n", "        else:\n", "            # Pad with repeated points\n", "            repeat_indices = np.random.choice(len(points), self.num_points - len(points), replace=True)\n", "            points = np.vstack([points, points[repeat_indices]])\n", "        \n", "        # Data augmentation (if enabled)\n", "        if self.augment:\n", "            # Random rotation around Z-axis\n", "            angle = np.random.uniform(0, 2 * np.pi)\n", "            cos_a, sin_a = np.cos(angle), np.sin(angle)\n", "            rotation_matrix = np.array([\n", "                [cos_a, -sin_a, 0],\n", "                [sin_a, cos_a, 0],\n", "                [0, 0, 1]\n", "            ])\n", "            points = points @ rotation_matrix.T\n", "            \n", "            # Random jittering\n", "            noise = np.random.normal(0, 0.01, points.shape)\n", "            points += noise\n", "        \n", "        # Convert to torch tensor\n", "        points_tensor = torch.FloatTensor(points)\n", "        \n", "        # Create PyTorch Geometric data object\n", "        data = Data(x=points_tensor)\n", "        \n", "        return data, patch['id']"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["def preprocess_patches_for_dgcnn(patches, num_points=1024):\n", "    \"\"\"\n", "    Preprocess patches for DGCNN input.\n", "    \"\"\"\n", "    print(f\"Preprocessing {len(patches)} patches for DGCNN input\")\n", "    print(f\"Target points per patch: {num_points}\")\n", "    \n", "    # Filter patches with sufficient points\n", "    valid_patches = [p for p in patches if len(p['points']) >= config.min_points_per_patch]\n", "    print(f\"Valid patches after filtering: {len(valid_patches)}\")\n", "    \n", "    # Create dataset\n", "    dataset = PointCloudDataset(valid_patches, num_points=num_points, augment=False)\n", "    \n", "    return dataset"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generating patches from point cloud...\n", "Generated 51 patches from point cloud\n", "Generated 51 patches\n", "Average points per patch: 108.6\n", "Min points per patch: 100\n", "Max points per patch: 125\n"]}], "source": ["# Generate patches from the loaded point cloud\n", "print(\"Generating patches from point cloud...\")\n", "patches = generate_patches_from_point_cloud(\n", "    points, \n", "    colors=colors,\n", "    patch_size=config.patch_size,\n", "    overlap_ratio=config.overlap_ratio,\n", "    min_points=config.min_points_per_patch\n", ")\n", "\n", "print(f\"Generated {len(patches)} patches\")\n", "if len(patches) > 0:\n", "    print(f\"Average points per patch: {np.mean([p['num_points'] for p in patches]):.1f}\")\n", "    print(f\"Min points per patch: {min([p['num_points'] for p in patches])}\")\n", "    print(f\"Max points per patch: {max([p['num_points'] for p in patches])}\")"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Preprocessing 51 patches for DGCNN input\n", "Target points per patch: 1024\n", "Valid patches after filtering: 51\n", "Created dataset with 51 patches\n", "Batch size: 8\n", "Number of batches: 7\n"]}], "source": ["# Preprocess patches for DGCNN\n", "dataset = preprocess_patches_for_dgcnn(patches, num_points=config.num_points)\n", "dataloader = DataLoader(dataset, batch_size=8, shuffle=False, num_workers=0)\n", "\n", "print(f\"Created dataset with {len(dataset)} patches\")\n", "print(f\"Batch size: 8\")\n", "print(f\"Number of batches: {len(dataloader)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Model Loading and Inference\n", "\n", "Load the trained DGCNN model and perform inference on patches."]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["def load_dgcnn_model(model_path, device):\n", "    \"\"\"\n", "    <PERSON>ad trained DGCNN model for inference.\n", "    \"\"\"\n", "    # Initialize model\n", "    model = DGCNNCSectionPile(\n", "        k=config.k_neighbors,\n", "        feature_dims=config.feature_dims,\n", "        num_classes=config.num_classes\n", "    )\n", "    \n", "    # Load model weights if available\n", "    if Path(model_path).exists():\n", "        print(f\"Loading model weights from: {model_path}\")\n", "        checkpoint = torch.load(model_path, map_location=device)\n", "        model.load_state_dict(checkpoint['model_state_dict'])\n", "        print(f\"Model loaded successfully (epoch {checkpoint.get('epoch', 'unknown')})\")\n", "    else:\n", "        print(f\"Warning: Model file not found at {model_path}\")\n", "        print(\"Using randomly initialized model (for demonstration purposes)\")\n", "    \n", "    model.to(device)\n", "    model.eval()\n", "    \n", "    return model"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["def run_inference(model, dataloader, device, confidence_threshold=0.7):\n", "    \"\"\"\n", "    Run inference on all patches using the DGCNN model.\n", "    \"\"\"\n", "    results = []\n", "    \n", "    print(f\"Running inference on {len(dataloader)} batches...\")\n", "    \n", "    with torch.no_grad():\n", "        for batch_idx, (batch_data, patch_ids) in enumerate(dataloader):\n", "            # Move data to device\n", "            batch_data = batch_data.to(device)\n", "            \n", "            # Forward pass\n", "            global_pred, point_pred = model(batch_data)\n", "            \n", "            # Apply softmax to get probabilities\n", "            global_probs = F.softmax(global_pred, dim=1)\n", "            point_probs = F.softmax(point_pred, dim=1)\n", "            \n", "            # Process each sample in the batch\n", "            batch_size = len(patch_ids)\n", "            for i in range(batch_size):\n", "                patch_id = patch_ids[i]\n", "                \n", "                # Global prediction (patch-level)\n", "                global_confidence = global_probs[i, 1].item()  # Probability of pile class\n", "                is_pile_patch = global_confidence > confidence_threshold\n", "                \n", "                # Point-wise predictions\n", "                start_idx = i * config.num_points\n", "                end_idx = start_idx + config.num_points\n", "                point_confidences = point_probs[start_idx:end_idx, 1].cpu().numpy()\n", "                \n", "                result = {\n", "                    'patch_id': patch_id,\n", "                    'global_confidence': global_confidence,\n", "                    'is_pile_patch': is_pile_patch,\n", "                    'point_confidences': point_confidences,\n", "                    'num_pile_points': (point_confidences > confidence_threshold).sum()\n", "                }\n", "                \n", "                results.append(result)\n", "            \n", "            if (batch_idx + 1) % 10 == 0:\n", "                print(f\"Processed {batch_idx + 1}/{len(dataloader)} batches\")\n", "    \n", "    print(f\"Inference completed. Processed {len(results)} patches.\")\n", "    return results"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Warning: Model file not found at ../../models/dgcnn_csection_pile.pth\n", "Using randomly initialized model (for demonstration purposes)\n", "Model parameters: 1,601,796 total, 1,601,796 trainable\n"]}], "source": ["# Load the DGCNN model\n", "model = load_dgcnn_model(model_path, device)\n", "\n", "# Display model information\n", "total_params = sum(p.numel() for p in model.parameters())\n", "trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)\n", "print(f\"Model parameters: {total_params:,} total, {trainable_params:,} trainable\")"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running inference on 7 batches...\n", "Inference completed. Processed 51 patches.\n", "Inference Results:\n", "Total patches processed: 51\n", "Patches with pile detections: 51\n", "Detection rate: 100.0%\n", "Total pile points detected: 52,224\n"]}], "source": ["# Run inference on all patches\n", "inference_results = run_inference(model, dataloader, device, confidence_threshold)\n", "\n", "# Display inference statistics\n", "pile_patches = [r for r in inference_results if r['is_pile_patch']]\n", "total_pile_points = sum(r['num_pile_points'] for r in inference_results)\n", "\n", "print(f\"Inference Results:\")\n", "print(f\"Total patches processed: {len(inference_results)}\")\n", "print(f\"Patches with pile detections: {len(pile_patches)}\")\n", "print(f\"Detection rate: {len(pile_patches)/len(inference_results)*100:.1f}%\")\n", "print(f\"Total pile points detected: {total_pile_points:,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Post-Processing and Pile Center Extraction\n", "\n", "Process detection results to extract pile centers and characteristics."]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["def extract_pile_centers(patches, inference_results, confidence_threshold=0.7):\n", "    \"\"\"\n", "    Extract pile centers from detection results.\n", "    \n", "    Parameters:\n", "    -----------\n", "    patches : list\n", "        Original patch data\n", "    inference_results : list\n", "        Inference results from DGCNN\n", "    confidence_threshold : float\n", "        Minimum confidence for pile detection\n", "        \n", "    Returns:\n", "    --------\n", "    pile_detections : list\n", "        List of detected pile centers with metadata\n", "    \"\"\"\n", "    pile_detections = []\n", "    \n", "    # Create patch lookup dictionary\n", "    patch_dict = {p['id']: p for p in patches}\n", "    \n", "    for result in inference_results:\n", "        if not result['is_pile_patch']:\n", "            continue\n", "            \n", "       # patch_id = result['patch_id']\n", "        patch_id = int(result['patch_id'])\n", "        patch = patch_dict[patch_id]\n", "        \n", "        # Get points with high pile confidence\n", "        point_confidences = result['point_confidences']\n", "        pile_mask = point_confidences > confidence_threshold\n", "        \n", "        if pile_mask.sum() < 10:  # Minimum points for valid detection\n", "            continue\n", "        \n", "        # Get pile points in original coordinates\n", "        patch_points = patch['original_points']\n", "        \n", "        # Sample points to match inference results\n", "        if len(patch_points) >= config.num_points:\n", "            indices = np.random.choice(len(patch_points), config.num_points, replace=False)\n", "            sampled_points = patch_points[indices]\n", "        else:\n", "            repeat_indices = np.random.choice(len(patch_points), config.num_points - len(patch_points), replace=True)\n", "            sampled_points = np.vstack([patch_points, patch_points[repeat_indices]])\n", "        \n", "        pile_points = sampled_points[pile_mask]\n", "        \n", "        # Calculate pile center\n", "        pile_center = pile_points.mean(axis=0)\n", "        \n", "        # Analyze pile geometry\n", "        geometry_features = analyze_c_section_geometry(pile_points)\n", "        \n", "        detection = {\n", "            'x': pile_center[0],\n", "            'y': pile_center[1],\n", "            'z': pile_center[2],\n", "            'pile_type': 'C-section',\n", "            'confidence': result['global_confidence'],\n", "            'num_points': len(pile_points),\n", "            'patch_id': patch_id,\n", "            'width': geometry_features['width'] if geometry_features else 0,\n", "            'height': geometry_features['height'] if geometry_features else 0,\n", "            'thickness': geometry_features['thickness'] if geometry_features else 0,\n", "            'c_shape_score': geometry_features['c_shape_score'] if geometry_features else 0,\n", "            'opening_ratio': geometry_features['opening_ratio'] if geometry_features else 0\n", "        }\n", "        \n", "        pile_detections.append(detection)\n", "    \n", "    print(f\"Extracted {len(pile_detections)} pile centers\")\n", "    return pile_detections"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["def cluster_nearby_detections(detections, distance_threshold=1.0):\n", "    \"\"\"\n", "    Cluster nearby pile detections to remove duplicates.\n", "    \n", "    Parameters:\n", "    -----------\n", "    detections : list\n", "        List of pile detections\n", "    distance_threshold : float\n", "        Maximum distance for clustering\n", "        \n", "    Returns:\n", "    --------\n", "    clustered_detections : list\n", "        Clustered and filtered detections\n", "    \"\"\"\n", "    if len(detections) == 0:\n", "        return []\n", "    \n", "    # Extract coordinates\n", "    coords = np.array([[d['x'], d['y'], d['z']] for d in detections])\n", "    \n", "    # Perform clustering\n", "    clustering = DBSCAN(eps=distance_threshold, min_samples=1)\n", "    cluster_labels = clustering.fit_predict(coords)\n", "    \n", "    clustered_detections = []\n", "    \n", "    # Process each cluster\n", "    for cluster_id in np.unique(cluster_labels):\n", "        if cluster_id == -1:  # Noise points\n", "            continue\n", "            \n", "        cluster_mask = cluster_labels == cluster_id\n", "        cluster_detections = [detections[i] for i in np.where(cluster_mask)[0]]\n", "        \n", "        # Select detection with highest confidence\n", "        best_detection = max(cluster_detections, key=lambda x: x['confidence'])\n", "        \n", "        # Update with cluster statistics\n", "        cluster_coords = coords[cluster_mask]\n", "        best_detection['cluster_size'] = len(cluster_detections)\n", "        best_detection['cluster_std'] = np.std(cluster_coords, axis=0).mean()\n", "        \n", "        clustered_detections.append(best_detection)\n", "    \n", "    print(f\"Clustered {len(detections)} detections into {len(clustered_detections)} final detections\")\n", "    return clustered_detections"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracted 51 pile centers\n", "Clustered 51 detections into 25 final detections\n", "Final pile detection results:\n", "Total detections: 25\n", "Average confidence: 0.502\n", "Confidence range: [0.501, 0.503]\n", "Average C-shape score: -58849.840\n"]}], "source": ["# Extract pile centers from inference results\n", "pile_detections = extract_pile_centers(patches, inference_results, confidence_threshold)\n", "\n", "# Cluster nearby detections to remove duplicates\n", "final_detections = cluster_nearby_detections(pile_detections, distance_threshold=1.5)\n", "\n", "print(f\"Final pile detection results:\")\n", "print(f\"Total detections: {len(final_detections)}\")\n", "if len(final_detections) > 0:\n", "    avg_confidence = np.mean([d['confidence'] for d in final_detections])\n", "    print(f\"Average confidence: {avg_confidence:.3f}\")\n", "    print(f\"Confidence range: [{min(d['confidence'] for d in final_detections):.3f}, {max(d['confidence'] for d in final_detections):.3f}]\")\n", "    \n", "    # C-section specific metrics\n", "    avg_c_score = np.mean([d['c_shape_score'] for d in final_detections])\n", "    print(f\"Average C-shape score: {avg_c_score:.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Results Export and Visualization\n", "\n", "Save detection results and create visualizations."]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["def save_detection_results(detections, output_dir, site_name, method):\n", "    \"\"\"\n", "    Save pile detection results to CSV file.\n", "    \n", "    Parameters:\n", "    -----------\n", "    detections : list\n", "        List of pile detections\n", "    output_dir : Path\n", "        Output directory\n", "    site_name : str\n", "        Site name\n", "    method : str\n", "        Detection method name\n", "        \n", "    Returns:\n", "    --------\n", "    output_file : Path\n", "        Path to saved CSV file\n", "    \"\"\"\n", "    # Create DataFrame\n", "    df = pd.DataFrame(detections)\n", "    \n", "    # Add metadata columns\n", "    df['site_name'] = site_name\n", "    df['detection_method'] = 'DGCNN'\n", "    df['ground_method'] = method\n", "    df['timestamp'] = pd.Timestamp.now()\n", "    \n", "    # Reorder columns\n", "    column_order = [\n", "        'site_name', 'pile_type', 'x', 'y', 'z', 'confidence',\n", "        'width', 'height', 'thickness', 'c_shape_score', 'opening_ratio',\n", "        'num_points', 'cluster_size', 'cluster_std', 'patch_id',\n", "        'detection_method', 'ground_method', 'timestamp'\n", "    ]\n", "    \n", "    # Only include columns that exist\n", "    available_columns = [col for col in column_order if col in df.columns]\n", "    df = df[available_columns]\n", "    \n", "    # Save to CSV\n", "    output_file = output_dir / f\"{site_name}_pile_detections_dgcnn_csection_{method}.csv\"\n", "    df.to_csv(output_file, index=False)\n", "    \n", "    print(f\"Saved {len(df)} detections to: {output_file}\")\n", "    return output_file"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["def create_detection_visualization(points, detections, output_dir, site_name):\n", "    \"\"\"\n", "    Create visualization of pile detections.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Original point cloud\n", "    detections : list\n", "        Pile detections\n", "    output_dir : Path\n", "        Output directory\n", "    site_name : str\n", "        Site name\n", "    \"\"\"\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))\n", "    \n", "    # Plot 1: Top-down view\n", "    ax1.scatter(points[:, 0], points[:, 1], c=points[:, 2], \n", "               cmap='terrain', alpha=0.5, s=0.1)\n", "    \n", "    if len(detections) > 0:\n", "        det_x = [d['x'] for d in detections]\n", "        det_y = [d['y'] for d in detections]\n", "        det_conf = [d['confidence'] for d in detections]\n", "        \n", "        scatter = ax1.scatter(det_x, det_y, c=det_conf, cmap='Reds', \n", "                            s=100, edgecolors='black', linewidth=1)\n", "        plt.colorbar(scatter, ax=ax1, label='Confidence')\n", "        \n", "        # Add detection labels\n", "        for i, det in enumerate(detections):\n", "            ax1.annotate(f'{i+1}', (det['x'], det['y']), \n", "                        xytext=(5, 5), textcoords='offset points',\n", "                        fontsize=8, color='white', weight='bold')\n", "    \n", "    ax1.set_xlabel('X (m)')\n", "    ax1.set_ylabel('Y (m)')\n", "    ax1.set_title(f'C-Section Pile Detections - Top View\\n{site_name}')\n", "    ax1.grid(True, alpha=0.3)\n", "    ax1.set_aspect('equal')\n", "    \n", "    # Plot 2: Confidence distribution\n", "    if len(detections) > 0:\n", "        confidences = [d['confidence'] for d in detections]\n", "        ax2.hist(confidences, bins=20, alpha=0.7, color='skyblue', edgecolor='black')\n", "        ax2.axvline(confidence_threshold, color='red', linestyle='--', \n", "                   label=f'Threshold: {confidence_threshold}')\n", "        ax2.set_xlabel('Confidence')\n", "        ax2.set_ylabel('Number of Detections')\n", "        ax2.set_title('Detection Confidence Distribution')\n", "        ax2.legend()\n", "        ax2.grid(True, alpha=0.3)\n", "    else:\n", "        ax2.text(0.5, 0.5, 'No detections found', \n", "                ha='center', va='center', transform=ax2.transAxes)\n", "        ax2.set_title('No Detections')\n", "    \n", "    # Plot 3: C-shape score distribution\n", "    if len(detections) > 0 and 'c_shape_score' in detections[0]:\n", "        c_scores = [d['c_shape_score'] for d in detections]\n", "        ax3.hist(c_scores, bins=15, alpha=0.7, color='lightgreen', edgecolor='black')\n", "        ax3.set_xlabel('C-Shape Score')\n", "        ax3.set_ylabel('Number of Detections')\n", "        ax3.set_title('C-Shape Score Distribution')\n", "        ax3.grid(True, alpha=0.3)\n", "    else:\n", "        ax3.text(0.5, 0.5, 'No C-shape scores available', \n", "                ha='center', va='center', transform=ax3.transAxes)\n", "        ax3.set_title('C-Shape Scores')\n", "    \n", "    # Plot 4: Opening ratio vs confidence\n", "    if len(detections) > 0 and 'opening_ratio' in detections[0]:\n", "        opening_ratios = [d['opening_ratio'] for d in detections]\n", "        confidences = [d['confidence'] for d in detections]\n", "        ax4.scatter(opening_ratios, confidences, alpha=0.7, s=50)\n", "        ax4.set_xlabel('Opening Ratio')\n", "        ax4.set_ylabel('Confidence')\n", "        ax4.set_title('Opening Ratio vs Confidence')\n", "        ax4.grid(True, alpha=0.3)\n", "    else:\n", "        ax4.text(0.5, 0.5, 'No opening ratio data', \n", "                ha='center', va='center', transform=ax4.transAxes)\n", "        ax4.set_title('Opening Ratio Analysis')\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # Save visualization\n", "    viz_file = output_dir / f\"{site_name}_pile_detections_dgcnn_csection_visualization.png\"\n", "    plt.savefig(viz_file, dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "    \n", "    print(f\"Saved visualization to: {viz_file}\")"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved 25 detections to: ../../output_runs/pile_detection/castro_pile_detections_dgcnn_csection_ransac.csv\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1200 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Saved visualization to: ../../output_runs/pile_detection/castro_pile_detections_dgcnn_csection_visualization.png\n", "\n", "C-Section Pile Detection Summary:\n", "Site: castro\n", "Ground segmentation method: ransac\n", "Input points: 644,537\n", "Generated patches: 51\n", "Processed patches: 51\n", "Final detections: 25\n", "Results saved to: ../../output_runs/pile_detection/castro_pile_detections_dgcnn_csection_ransac.csv\n"]}], "source": ["# Save detection results\n", "output_file = save_detection_results(final_detections, output_dir, site_name, ground_method)\n", "\n", "# Create visualization\n", "create_detection_visualization(points, final_detections, output_dir, site_name)\n", "\n", "# Display summary statistics\n", "print(f\"\\nC-Section Pile Detection Summary:\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Ground segmentation method: {ground_method}\")\n", "print(f\"Input points: {len(points):,}\")\n", "print(f\"Generated patches: {len(patches)}\")\n", "print(f\"Processed patches: {len(inference_results)}\")\n", "print(f\"Final detections: {len(final_detections)}\")\n", "print(f\"Results saved to: {output_file}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Analysis and Inference\n", "\n", "Analyze the detection results and provide insights."]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Detection Analysis:\n", "Confidence statistics:\n", "  Mean: 0.502\n", "  Std: 0.000\n", "  Min: 0.501\n", "  Max: 0.503\n", "\n", "C-Section characteristics:\n", "  Average C-shape score: -58849.840\n", "  Average opening ratio: 0.157\n", "  C-shape score range: [-198074.288, -0.000]\n", "\n", "Spatial distribution:\n", "  X range: [707379.66, 707794.86] m\n", "  Y range: [4693007.45, 4693136.72] m\n", "  Z range: [53.19, 59.21] m\n", "\n", "Geometric characteristics:\n", "  Average width: 1.972 m\n", "  Average height: 2.773 m\n", "\n", "Inference:\n", "The DGCNN model detected 25 C-section pile candidates\n", "with an average confidence of 50.2%.\n", "Low C-shape scores may indicate weak geometric evidence or challenging conditions.\n", "Low confidence detections suggest challenging conditions or model limitations.\n"]}], "source": ["# Analysis of detection results\n", "if len(final_detections) > 0:\n", "    print(\"Detection Analysis:\")\n", "    \n", "    # Confidence statistics\n", "    confidences = [d['confidence'] for d in final_detections]\n", "    print(f\"Confidence statistics:\")\n", "    print(f\"  Mean: {np.mean(confidences):.3f}\")\n", "    print(f\"  Std: {np.std(confidences):.3f}\")\n", "    print(f\"  Min: {np.min(confidences):.3f}\")\n", "    print(f\"  Max: {np.max(confidences):.3f}\")\n", "    \n", "    # C-section specific analysis\n", "    if 'c_shape_score' in final_detections[0]:\n", "        c_scores = [d['c_shape_score'] for d in final_detections]\n", "        opening_ratios = [d['opening_ratio'] for d in final_detections]\n", "        \n", "        print(f\"\\nC-Section characteristics:\")\n", "        print(f\"  Average C-shape score: {np.mean(c_scores):.3f}\")\n", "        print(f\"  Average opening ratio: {np.mean(opening_ratios):.3f}\")\n", "        print(f\"  C-shape score range: [{np.min(c_scores):.3f}, {np.max(c_scores):.3f}]\")\n", "    \n", "    # Spatial distribution\n", "    x_coords = [d['x'] for d in final_detections]\n", "    y_coords = [d['y'] for d in final_detections]\n", "    z_coords = [d['z'] for d in final_detections]\n", "    \n", "    print(f\"\\nSpatial distribution:\")\n", "    print(f\"  X range: [{np.min(x_coords):.2f}, {np.max(x_coords):.2f}] m\")\n", "    print(f\"  Y range: [{np.min(y_coords):.2f}, {np.max(y_coords):.2f}] m\")\n", "    print(f\"  Z range: [{np.min(z_coords):.2f}, {np.max(z_coords):.2f}] m\")\n", "    \n", "    # Geometric characteristics\n", "    if 'width' in final_detections[0]:\n", "        widths = [d['width'] for d in final_detections if d['width'] > 0]\n", "        heights = [d['height'] for d in final_detections if d['height'] > 0]\n", "        \n", "        if widths:\n", "            print(f\"\\nGeometric characteristics:\")\n", "            print(f\"  Average width: {np.mean(widths):.3f} m\")\n", "            print(f\"  Average height: {np.mean(heights):.3f} m\")\n", "    \n", "    print(f\"\\nInference:\")\n", "    print(f\"The DGCNN model detected {len(final_detections)} C-section pile candidates\")\n", "    print(f\"with an average confidence of {np.mean(confidences):.1%}.\")\n", "    \n", "    if 'c_shape_score' in final_detections[0]:\n", "        avg_c_score = np.mean([d['c_shape_score'] for d in final_detections])\n", "        if avg_c_score > 0.3:\n", "            print(\"High C-shape scores indicate strong geometric evidence for C-section profiles.\")\n", "        elif avg_c_score > 0.1:\n", "            print(\"Moderate C-shape scores suggest possible C-section characteristics.\")\n", "        else:\n", "            print(\"Low C-shape scores may indicate weak geometric evidence or challenging conditions.\")\n", "    \n", "    if np.mean(confidences) > 0.8:\n", "        print(\"High confidence detections suggest reliable pile identification.\")\n", "    elif np.mean(confidences) > 0.6:\n", "        print(\"Moderate confidence detections may require manual verification.\")\n", "    else:\n", "        print(\"Low confidence detections suggest challenging conditions or model limitations.\")\n", "        \n", "else:\n", "    print(\"No C-section piles detected in the point cloud.\")\n", "    print(\"This could indicate:\")\n", "    print(\"- No C-section piles present in the surveyed area\")\n", "    print(\"- Insufficient point cloud quality or density\")\n", "    print(\"- Model confidence threshold too high\")\n", "    print(\"- Need for model retraining on site-specific data\")\n", "    print(\"- C-section profiles may be oriented differently than expected\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import ezdxf\n", "\n", "doc = ezdxf.readfile(\"Drawing of Earthworks1_2.dwg\")\n", "msp = doc.modelspace()\n", "\n", "pile_coords = []\n", "for entity in msp.query('CIRCLE'):  # or LINE, POINT, INSERT, etc.\n", "    x, y, z = entity.dxf.center\n", "    pile_coords.append((x, y, z))\n", "\n", "print(\"Extracted pile positions:\", pile_coords[:5])\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}