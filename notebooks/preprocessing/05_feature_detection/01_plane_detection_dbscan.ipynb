{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# DBSCAN Clustering for Solar Panel Segmentation\n", "\n", "This notebook implements Density-Based Spatial Clustering of Applications with Noise (DBSCAN) for segmenting detected planes into individual solar panels. It demonstrates how to:\n", "\n", "1. Load planes detected by RANSAC\n", "2. Apply DBSCAN clustering to segment planes into individual panels\n", "3. Visualize the segmented panels\n", "4. Calculate geometric properties for each panel\n", "5. Save the results for further analysis\n", "\n", "**Author:** <PERSON><PERSON><PERSON>  \n", "**Date:** June 2024"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Installation\n", "\n", "First, let's install the necessary dependencies and import required libraries."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "# Uncomment and run this cell if you need to install the packages\n", "\n", "# !pip install numpy matplotlib open3d scikit-learn"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import random\n", "import time\n", "import os\n", "import logging\n", "import sys\n", "from sklearn.cluster import DBSCAN\n", "from sklearn.neighbors import NearestNeighbors\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Try to import Open3D for point cloud visualization\n", "try:\n", "    import open3d as o3d\n", "    O3D_SUPPORT = True\n", "    logger.info(\"Open3D support is available.\")\n", "except ImportError:\n", "    logger.warning(\"open3d not installed. Some visualizations may not be available.\")\n", "    O3D_SUPPORT = False\n", "\n", "# Check if running in Google Colab\n", "try:\n", "    from google.colab import drive\n", "    drive.mount('/content/gdrive')\n", "    IN_COLAB = True\n", "    logger.info(\"Google Drive mounted successfully.\")\n", "except ImportError:\n", "    IN_COLAB = False\n", "    logger.info(\"Not running in Google Colab. Using local file system.\")\n", "\n", "# Add the src directory to the path so we can import our modules\n", "module_path = os.path.abspath(os.path.join(os.path.dirname(os.getcwd()), '..'))\n", "if module_path not in sys.path:\n", "    sys.path.append(module_path)\n", "    print(f\"Added {module_path} to sys.path\")\n", "\n", "# Import our DBSCAN implementation\n", "try:\n", "    from src.plane_detection.dbscan import estimate_dbscan_eps, compute_point_normals, cluster_points_dbscan, segment_plane_into_panels, segment_planes_into_panels\n", "    DBSCAN_MODULE_AVAILABLE = True\n", "    print(\"Successfully imported DBSCAN module from src.plane_detection.dbscan\")\n", "except ImportError as e:\n", "    DBSCAN_MODULE_AVAILABLE = False\n", "    print(f\"Could not import DBSCAN module: {e}\")\n", "    print(\"Will use the notebook implementation instead.\")\n", "\n", "# Print version information\n", "print(\"NumPy version:\", np.__version__)\n", "print(\"scikit-learn version:\", sklearn.__version__)\n", "if O3D_SUPPORT:\n", "    print(\"Open3D version:\", o3d.__version__)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Loading Detected Planes\n", "\n", "Now let's load the planes detected by the RANSAC algorithm. We'll load both the plane information (saved as a NumPy file) and the individual plane point clouds (saved as PLY files)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define paths to your files\n", "if IN_COLAB:\n", "    # Google Drive paths\n", "    base_path = '/content/gdrive/MyDrive/pc-experiment'\n", "else:\n", "    # Local paths - adjust as needed\n", "    base_path = '../data/pc-experiment'\n", "\n", "# Create output directory if it doesn't exist\n", "output_dir = os.path.join(base_path, 'output')\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "# Create subdirectories for RANSAC and DBSCAN\n", "ransac_output_dir = os.path.join(output_dir, 'ransac')\n", "dbscan_output_dir = os.path.join(output_dir, 'dbscan')\n", "os.makedirs(ransac_output_dir, exist_ok=True)\n", "os.makedirs(dbscan_output_dir, exist_ok=True)\n", "\n", "# Path to detected planes file (check both locations)\n", "planes_file_old = os.path.join(output_dir, 'detected_planes.npy')  # Old location\n", "planes_file_new = os.path.join(ransac_output_dir, 'detected_planes.npy')  # New location\n", "\n", "# Check if the planes file exists in either location\n", "if os.path.exists(planes_file_new):\n", "    planes_file = planes_file_new\n", "    print(f\"Found detected planes file at {planes_file}\")\n", "    # Load the planes data\n", "    planes_data = np.load(planes_file, allow_pickle=True).item()\n", "    print(f\"Loaded data for {planes_data['num_planes']} planes\")\n", "    \n", "    # Check if original indices are preserved\n", "    if 'planes' in planes_data and len(planes_data['planes']) > 0 and 'original_indices' in planes_data['planes'][0]:\n", "        print(\"Original point indices are preserved in the planes data.\")\n", "        ORIGINAL_INDICES_PRESERVED = True\n", "    else:\n", "        print(\"Original point indices are NOT preserved in the planes data.\")\n", "        ORIGINAL_INDICES_PRESERVED = False\n", "        \n", "elif os.path.exists(planes_file_old):\n", "    planes_file = planes_file_old\n", "    print(f\"Found detected planes file at {planes_file} (old location)\")\n", "    # Load the planes data\n", "    planes_data = np.load(planes_file, allow_pickle=True).item()\n", "    print(f\"Loaded data for {planes_data['num_planes']} planes\")\n", "    ORIGINAL_INDICES_PRESERVED = False\n", "    print(\"Original point indices are NOT preserved in the planes data (old format).\")\n", "else:\n", "    print(f\"Detected planes file not found at {planes_file_new} or {planes_file_old}\")\n", "    print(\"You may need to run the RANSAC notebook first or adjust the path.\")\n", "    # Create dummy data for testing\n", "    planes_data = {'num_planes': 0, 'planes': []}\n", "    ORIGINAL_INDICES_PRESERVED = False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_plane_points(output_dir, num_planes):\n", "    \"\"\"\n", "    Load point clouds for each detected plane.\n", "    \n", "    Parameters:\n", "    -----------\n", "    output_dir : str\n", "        Directory containing the plane point cloud files\n", "    num_planes : int\n", "        Number of planes to load\n", "        \n", "    Returns:\n", "    --------\n", "    plane_points : list of numpy.ndarray\n", "        List of point clouds for each plane\n", "    \"\"\"\n", "    if not O3D_SUPPORT:\n", "        logger.warning(\"Open3D not available, cannot load plane point clouds\")\n", "        return []\n", "        \n", "    plane_points = []\n", "    \n", "    for i in range(num_planes):\n", "        plane_file = os.path.join(output_dir, f'plane_{i+1}.ply')\n", "        if os.path.exists(plane_file):\n", "            # Load the point cloud\n", "            pcd = o3d.io.read_point_cloud(plane_file)\n", "            points = np.asarray(pcd.points)\n", "            plane_points.append(points)\n", "            logger.info(f\"Loaded plane {i+1} with {points.shape[0]} points\")\n", "        else:\n", "            logger.warning(f\"Plane file not found: {plane_file}\")\n", "    \n", "    return plane_points\n", "\n", "# Load the plane point clouds\n", "plane_points = load_plane_points(output_dir, planes_data['num_planes'])\n", "\n", "# If no planes were loaded, create synthetic data for testing\n", "if len(plane_points) == 0:\n", "    print(\"Creating synthetic plane data for testing...\")\n", "    # Create 3 synthetic planes\n", "    plane_points = []\n", "    for i in range(3):\n", "        # Create a grid of points on a plane\n", "        x = np.linspace(-5, 5, 20)\n", "        y = np.linspace(-5, 5, 20)\n", "        xx, yy = np.meshgrid(x, y)\n", "        \n", "        # Add some noise and offset\n", "        zz = 0.1 * np.random.randn(xx.shape[0], xx.shape[1]) + i * 2\n", "        \n", "        # Combine into points\n", "        points = np.column_stack((xx.flatten(), yy.flatten(), zz.flatten()))\n", "        \n", "        # Add to list\n", "        plane_points.append(points)\n", "        print(f\"Created synthetic plane {i+1} with {points.shape[0]} points\")\n", "    \n", "    # Update planes_data\n", "    planes_data['num_planes'] = len(plane_points)\n", "    planes_data['planes'] = [{'equation': [0, 0, 1, -i*2]} for i in range(len(plane_points))]\n", "\n", "print(f\"\\nTotal planes loaded: {len(plane_points)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. DBSCAN Clustering Implementation\n", "\n", "Now let's implement DBSCAN clustering to segment the detected planes into individual solar panels. We'll use both spatial coordinates and normal vectors for clustering."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def estimate_dbscan_eps(points, n_samples=1000, n_neighbors=10):\n", "    \"\"\"\n", "    Estimate a good eps value for DBSCAN based on k-distance graph.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud data (N x D)\n", "    n_samples : int\n", "        Number of samples to use for estimation\n", "    n_neighbors : int\n", "        Number of neighbors to consider\n", "        \n", "    Returns:\n", "    --------\n", "    eps : float\n", "        Estimated eps value for DBSCAN\n", "    \"\"\"\n", "    # Subsample points if there are too many\n", "    if points.shape[0] > n_samples:\n", "        indices = np.random.choice(points.shape[0], n_samples, replace=False)\n", "        points_sample = points[indices]\n", "    else:\n", "        points_sample = points\n", "    \n", "    # Compute distances to k nearest neighbors\n", "    nbrs = NearestNeighbors(n_neighbors=n_neighbors+1).fit(points_sample)\n", "    distances, indices = nbrs.kneighbors(points_sample)\n", "    \n", "    # Sort distances to the k-th nearest neighbor\n", "    k_distances = np.sort(distances[:, n_neighbors])\n", "    \n", "    # Plot k-distance graph\n", "    plt.figure(figsize=(10, 6))\n", "    plt.plot(range(len(k_distances)), k_distances)\n", "    plt.xlabel('Points (sorted by distance)')\n", "    plt.ylabel(f'Distance to {n_neighbors}-th nearest neighbor')\n", "    plt.title('k-distance graph')\n", "    plt.grid(True)\n", "    plt.show()\n", "    \n", "    # Find the elbow point (where the curve starts to flatten)\n", "    # This is a simple heuristic - you might want to use a more sophisticated method\n", "    eps_index = np.argmax(np.diff(np.diff(k_distances)))\n", "    eps = k_distances[eps_index]\n", "    \n", "    print(f\"Estimated eps value: {eps:.4f}\")\n", "    return eps\n", "\n", "def compute_normals(points, radius=0.1, max_nn=30):\n", "    \"\"\"\n", "    Compute normal vectors for points using Open3D.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud data (N x 3)\n", "    radius : float\n", "        Radius for normal estimation\n", "    max_nn : int\n", "        Maximum number of neighbors to consider\n", "        \n", "    Returns:\n", "    --------\n", "    normals : numpy.n<PERSON><PERSON>\n", "        Normal vectors (N x 3)\n", "    \"\"\"\n", "    if not O3D_SUPPORT:\n", "        logger.warning(\"Open3D not available, cannot compute normals\")\n", "        return np.zeros((points.shape[0], 3))\n", "    \n", "    # Create Open3D point cloud\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(points)\n", "    \n", "    # Estimate normals\n", "    pcd.estimate_normals(search_param=o3d.geometry.KDTreeSearchParamHybrid(radius=radius, max_nn=max_nn))\n", "    \n", "    # Orient normals consistently\n", "    pcd.orient_normals_consistent_tangent_plane(k=max_nn)\n", "    \n", "    # Get normals as numpy array\n", "    normals = np.asarray(pcd.normals)\n", "    \n", "    return normals\n", "\n", "def cluster_points_dbscan(points, eps=0.1, min_samples=10, use_normals=True, normal_weight=0.5):\n", "    \"\"\"\n", "    Cluster points using DBSCAN.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud data (N x 3)\n", "    eps : float\n", "        Maximum distance between two samples for them to be considered as in the same neighborhood\n", "    min_samples : int\n", "        Minimum number of samples in a neighborhood for a point to be considered as a core point\n", "    use_normals : bool\n", "        Whether to use normal vectors for clustering\n", "    normal_weight : float\n", "        Weight for normal vectors in the feature vector (0-1)\n", "        \n", "    Returns:\n", "    --------\n", "    labels : numpy.ndarray\n", "        Cluster labels for each point (-1 for noise)\n", "    n_clusters : int\n", "        Number of clusters\n", "    \"\"\"\n", "    # Compute normals if needed\n", "    if use_normals:\n", "        normals = compute_normals(points)\n", "        \n", "        # Create feature vector with both coordinates and normals\n", "        coord_weight = 1.0 - normal_weight\n", "        features = np.hstack((coord_weight * points, normal_weight * normals))\n", "    else:\n", "        features = points\n", "    \n", "    # Standardize features\n", "    scaler = StandardScaler()\n", "    features_scaled = scaler.fit_transform(features)\n", "    \n", "    # Apply DBSCAN\n", "    db = DBSCAN(eps=eps, min_samples=min_samples).fit(features_scaled)\n", "    labels = db.labels_\n", "    \n", "    # Number of clusters (excluding noise)\n", "    n_clusters = len(set(labels)) - (1 if -1 in labels else 0)\n", "    \n", "    return labels, n_clusters"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Apply DBSCAN to Each Plane\n", "\n", "Now let's apply DBSCAN clustering to each detected plane to segment it into individual solar panels."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Process each plane using either the modular implementation or the notebook implementation\n", "all_clusters = []\n", "all_cluster_points = []\n", "\n", "if DBSCAN_MODULE_AVAILABLE and ORIGINAL_INDICES_PRESERVED:\n", "    print(\"\\n=== Using Modular DBSCAN Implementation ===\\n\")\n", "    \n", "    # Use the modular implementation to segment all planes at once\n", "    panels_file = segment_planes_into_panels(\n", "        planes_file,\n", "        dbscan_output_dir,\n", "        eps=None,  # Will be estimated for each plane\n", "        min_samples=None,  # Will be estimated for each plane\n", "        use_normals=True,\n", "        normal_weight=0.3,\n", "        save_panels=True,\n", "        point_cloud_format='ply'\n", "    )\n", "    \n", "    # Load the panels data\n", "    panels_data = np.load(panels_file, allow_pickle=True).item()\n", "    print(f\"Loaded data for {panels_data['num_panels']} panels from {panels_file}\")\n", "    \n", "    # Convert to our all_clusters format for visualization\n", "    for i, panel in enumerate(panels_data['panels']):\n", "        # Get the panel points\n", "        panel_file = os.path.join(dbscan_output_dir, f'plane_{panel[\"plane_index\"]+1}_panel_{i+1}.ply')\n", "        if os.path.exists(panel_file) and O3D_SUPPORT:\n", "            pcd = o3d.io.read_point_cloud(panel_file)\n", "            panel_points = np.asarray(pcd.points)\n", "            \n", "            # Add to our lists\n", "            all_clusters.append({\n", "                'plane_id': panel['plane_index'],\n", "                'cluster_id': i,\n", "                'num_points': panel['num_points'],\n", "                'centroid': panel['centroid'],\n", "                'plane_equation': panel['plane_equation'],\n", "                'original_indices': panel['original_indices']\n", "            })\n", "            all_cluster_points.append(panel_points)\n", "            \n", "            print(f\"Panel {i+1}: {panel['num_points']} points\")\n", "    \n", "    print(f\"\\nTotal panels found: {len(all_clusters)}\")\n", "    \n", "else:\n", "    print(\"\\n=== Using Notebook DBSCAN Implementation ===\\n\")\n", "    \n", "    # Process each plane using the notebook implementation\n", "    for i, plane_point_cloud in enumerate(plane_points):\n", "        print(f\"\\n=== Processing Plane {i+1}/{len(plane_points)} ===\\n\")\n", "        \n", "        # Skip if the plane has too few points\n", "        if plane_point_cloud.shape[0] < 10:\n", "            print(f\"Skipping plane {i+1} - too few points ({plane_point_cloud.shape[0]})\")\n", "            continue\n", "        \n", "        # Get original indices for this plane if available\n", "        if ORIGINAL_INDICES_PRESERVED and i < len(planes_data['planes']):\n", "            original_indices = np.array(planes_data['planes'][i]['original_indices'])\n", "            print(f\"Using {len(original_indices)} original indices from planes data\")\n", "        else:\n", "            # Create dummy indices\n", "            original_indices = np.arange(plane_point_cloud.shape[0])\n", "            print(\"Using dummy indices (original indices not preserved)\")\n", "        \n", "        # Estimate a good eps value for DBSCAN\n", "        print(\"Estimating DBSCAN parameters...\")\n", "        if DBSCAN_MODULE_AVAILABLE:\n", "            eps = estimate_dbscan_eps(plane_point_cloud)\n", "        else:\n", "            eps = estimate_dbscan_eps(plane_point_cloud)\n", "        \n", "        # Set min_samples based on the number of points\n", "        min_samples = max(10, int(plane_point_cloud.shape[0] * 0.01))  # At least 1% of points\n", "        print(f\"Using min_samples = {min_samples}\")\n", "        \n", "        # Apply DBSCAN clustering\n", "        print(\"Applying DBSCAN clustering...\")\n", "        if DBSCAN_MODULE_AVAILABLE:\n", "            # Use the modular implementation\n", "            panels = segment_plane_into_panels(\n", "                plane_point_cloud,\n", "                original_indices,\n", "                eps=eps,\n", "                min_samples=min_samples,\n", "                use_normals=True,\n", "                normal_weight=0.3\n", "            )\n", "            \n", "            # Print clustering results\n", "            print(f\"DBSCAN found {len(panels)} panels\")\n", "            \n", "            # Add panels to our lists\n", "            for j, panel in enumerate(panels):\n", "                all_clusters.append({\n", "                    'plane_id': i,\n", "                    'cluster_id': j,\n", "                    'num_points': len(panel['points']),\n", "                    'centroid': panel['centroid'],\n", "                    'plane_equation': planes_data['planes'][i]['equation'] if i < len(planes_data['planes']) else None,\n", "                    'original_indices': panel['original_indices']\n", "                })\n", "                all_cluster_points.append(panel['points'])\n", "                \n", "                print(f\"Panel {j+1}: {len(panel['points'])} points\")\n", "        else:\n", "            # Use the notebook implementation\n", "            labels, n_clusters = cluster_points_dbscan(\n", "                plane_point_cloud, \n", "                eps=eps, \n", "                min_samples=min_samples,\n", "                use_normals=True,\n", "                normal_weight=0.3\n", "            )\n", "            \n", "            # Print clustering results\n", "            n_noise = list(labels).count(-1)\n", "            print(f\"DBSCAN found {n_clusters} clusters and {n_noise} noise points\")\n", "            print(f\"Noise percentage: {n_noise / plane_point_cloud.shape[0]:.2%}\")\n", "            \n", "            # Store the clusters for this plane\n", "            plane_clusters = []\n", "            plane_cluster_points = []\n", "            \n", "            # Process each cluster\n", "            for cluster_id in range(n_clusters):\n", "                # Get points in this cluster\n", "                cluster_mask = (labels == cluster_id)\n", "                cluster_points = plane_point_cloud[cluster_mask]\n", "                cluster_original_indices = original_indices[cluster_mask]\n", "                \n", "                # Skip if the cluster has too few points\n", "                if cluster_points.shape[0] < 10:\n", "                    print(f\"Skipping cluster {cluster_id} - too few points ({cluster_points.shape[0]})\")\n", "                    continue\n", "                \n", "                # Add to the list\n", "                plane_clusters.append({\n", "                    'plane_id': i,\n", "                    'cluster_id': cluster_id,\n", "                    'num_points': cluster_points.shape[0],\n", "                    'centroid': np.mean(cluster_points, axis=0),\n", "                    'plane_equation': planes_data['planes'][i]['equation'] if i < len(planes_data['planes']) else None,\n", "                    'original_indices': cluster_original_indices\n", "                })\n", "                plane_cluster_points.append(cluster_points)\n", "                \n", "                print(f\"Cluster {cluster_id}: {cluster_points.shape[0]} points\")\n", "            \n", "            # Add to the overall lists\n", "            all_clusters.extend(plane_clusters)\n", "            all_cluster_points.extend(plane_cluster_points)\n", "            \n", "            print(f\"Found {len(plane_clusters)} valid clusters in plane {i+1}\")\n", "    \n", "    print(f\"\\nTotal clusters found across all planes: {len(all_clusters)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Visualize Clusters\n", "\n", "Now let's visualize the clusters to see how well DBSCAN has segmented the planes into individual solar panels."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_clusters_open3d(points_list, title=\"Clustered Point Cloud\"):\n", "    \"\"\"\n", "    Visualize clusters using Open3D.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points_list : list of numpy.ndarray\n", "        List of point clouds for each cluster\n", "    title : str\n", "        Window title\n", "    \"\"\"\n", "    if not O3D_SUPPORT:\n", "        logger.warning(\"Open3D not available, skipping visualization\")\n", "        return\n", "        \n", "    # Define colors for clusters\n", "    colors = [\n", "        [1, 0, 0],    # Red\n", "        [0, 1, 0],    # Green\n", "        [0, 0, 1],    # Blue\n", "        [1, 1, 0],    # Yellow\n", "        [1, 0, 1],    # <PERSON><PERSON><PERSON>\n", "        [0, 1, 1],    # <PERSON><PERSON>\n", "        [0.5, 0.5, 0], # <PERSON>\n", "        [0.5, 0, 0.5], # Purple\n", "        [0, 0.5, 0.5], # <PERSON><PERSON>\n", "        [1, 0.5, 0]   # Orange\n", "    ]\n", "    \n", "    # Create a list of geometries to visualize\n", "    geometries = []\n", "    \n", "    # Add each cluster with a different color\n", "    for i, points in enumerate(points_list):\n", "        if points.shape[0] == 0:\n", "            continue\n", "            \n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(points)\n", "        pcd.paint_uniform_color(colors[i % len(colors)])\n", "        geometries.append(pcd)\n", "    \n", "    # Visualize\n", "    o3d.visualization.draw_geometries(geometries, window_name=title)\n", "\n", "def visualize_clusters_matplotlib(points_list, title=\"Clustered Point Cloud\"):\n", "    \"\"\"\n", "    Visualize clusters using Matplotlib.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points_list : list of numpy.ndarray\n", "        List of point clouds for each cluster\n", "    title : str\n", "        Plot title\n", "    \"\"\"\n", "    # Create a new figure\n", "    fig = plt.figure(figsize=(12, 10))\n", "    ax = fig.add_subplot(111, projection='3d')\n", "    \n", "    # Define colors for clusters\n", "    colors = ['r', 'g', 'b', 'y', 'm', 'c', 'orange', 'purple', 'brown', 'pink']\n", "    \n", "    # Plot each cluster with a different color\n", "    for i, points in enumerate(points_list):\n", "        if points.shape[0] == 0:\n", "            continue\n", "            \n", "        ax.scatter(\n", "            points[:, 0], points[:, 1], points[:, 2],\n", "            c=colors[i % len(colors)],\n", "            s=5,\n", "            label=f\"Cluster {i}\"\n", "        )\n", "    \n", "    # Set labels and title\n", "    ax.set_xlabel('X')\n", "    ax.set_ylabel('Y')\n", "    ax.set_zlabel('Z')\n", "    ax.set_title(title)\n", "    \n", "    # Add legend\n", "    ax.legend()\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize all clusters\n", "print(\"\\n=== Visualizing All Clusters ===\\n\")\n", "\n", "# Visualize using Matplotlib\n", "visualize_clusters_matplotlib(all_cluster_points, title=\"All Clusters\")\n", "\n", "# Visualize using Open3D if available\n", "if O3D_SUPPORT and not IN_COLAB:\n", "    visualize_clusters_open3d(all_cluster_points, title=\"All Clusters\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Calculate Geometric Properties\n", "\n", "Now let's calculate geometric properties for each cluster, such as area, dimensions, and orientation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_cluster_properties(points, plane_equation):\n", "    \"\"\"\n", "    Calculate geometric properties of a cluster.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud data for the cluster (N x 3)\n", "    plane_equation : list\n", "        Plane equation coefficients [a, b, c, d]\n", "        \n", "    Returns:\n", "    --------\n", "    properties : dict\n", "        Dictionary of geometric properties\n", "    \"\"\"\n", "    # Calculate centroid\n", "    centroid = np.mean(points, axis=0)\n", "    \n", "    # Calculate covariance matrix\n", "    centered_points = points - centroid\n", "    cov = np.dot(centered_points.T, centered_points) / points.shape[0]\n", "    \n", "    # Perform eigendecomposition\n", "    eigenvalues, eigenvectors = np.linalg.eigh(cov)\n", "    \n", "    # Sort eigenvalues and eigenvectors in descending order\n", "    idx = eigenvalues.argsort()[::-1]\n", "    eigenvalues = eigenvalues[idx]\n", "    eigenvectors = eigenvectors[:, idx]\n", "    \n", "    # The first two eigenvectors define the plane\n", "    v1 = eigenvectors[:, 0]\n", "    v2 = eigenvectors[:, 1]\n", "    normal = eigenvectors[:, 2]  # The third eigenvector is the normal\n", "    \n", "    # Ensure the normal vector is consistent with the plane equation\n", "    if plane_equation is not None:\n", "        plane_normal = np.array(plane_equation[:3])\n", "        plane_normal = plane_normal / np.linalg.norm(plane_normal)\n", "        if np.dot(normal, plane_normal) < 0:\n", "            normal = -normal\n", "    \n", "    # Project points onto the plane\n", "    projected_points = np.zeros_like(centered_points)\n", "    for i in range(centered_points.shape[0]):\n", "        # Project onto the plane defined by v1 and v2\n", "        p = centered_points[i]\n", "        projected_points[i] = np.dot(p, v1) * v1 + np.dot(p, v2) * v2\n", "    \n", "    # Calculate the 2D bounding box in the plane\n", "    v1_coords = np.dot(projected_points, v1)\n", "    v2_coords = np.dot(projected_points, v2)\n", "    \n", "    min_v1, max_v1 = np.min(v1_coords), np.max(v1_coords)\n", "    min_v2, max_v2 = np.min(v2_coords), np.max(v2_coords)\n", "    \n", "    # Calculate dimensions\n", "    length = max_v1 - min_v1\n", "    width = max_v2 - min_v2\n", "    area = length * width\n", "    \n", "    # Calculate tilt angle (angle between normal and z-axis)\n", "    z_axis = np.array([0, 0, 1])\n", "    tilt_angle = np.arccos(np.abs(np.dot(normal, z_axis))) * 180 / np.pi\n", "    \n", "    # Calculate azimuth angle (angle between projection of normal on xy-plane and x-axis)\n", "    normal_xy = np.array([normal[0], normal[1], 0])\n", "    if np.linalg.norm(normal_xy) > 1e-6:  # Avoid division by zero\n", "        normal_xy = normal_xy / np.linalg.norm(normal_xy)\n", "        x_axis = np.array([1, 0, 0])\n", "        azimuth = np.arccos(np.dot(normal_xy, x_axis)) * 180 / np.pi\n", "        # Adjust for the quadrant\n", "        if normal[1] < 0:\n", "            azimuth = 360 - azimuth\n", "    else:\n", "        azimuth = 0  # Normal is parallel to z-axis\n", "    \n", "    # Return properties\n", "    return {\n", "        'centroid': centroid,\n", "        'normal': normal,\n", "        'length': length,\n", "        'width': width,\n", "        'area': area,\n", "        'tilt_angle': tilt_angle,\n", "        'azimuth': azimuth,\n", "        'eigenvalues': eigenvalues,\n", "        'eigenvectors': eigenvectors,\n", "        'num_points': points.shape[0]\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate properties for each cluster\n", "print(\"\\n=== Calculating Geometric Properties ===\\n\")\n", "\n", "for i, (cluster, points) in enumerate(zip(all_clusters, all_cluster_points)):\n", "    # Calculate properties\n", "    properties = calculate_cluster_properties(points, cluster['plane_equation'])\n", "    \n", "    # Add properties to the cluster\n", "    cluster.update(properties)\n", "    \n", "    # Print properties\n", "    print(f\"Cluster {i} (Plane {cluster['plane_id']}, Cluster {cluster['cluster_id']})\")\n", "    print(f\"  Number of points: {cluster['num_points']}\")\n", "    print(f\"  Centroid: [{cluster['centroid'][0]:.2f}, {cluster['centroid'][1]:.2f}, {cluster['centroid'][2]:.2f}]\")\n", "    print(f\"  Normal: [{cluster['normal'][0]:.4f}, {cluster['normal'][1]:.4f}, {cluster['normal'][2]:.4f}]\")\n", "    print(f\"  Dimensions: {cluster['length']:.2f} x {cluster['width']:.2f} units\")\n", "    print(f\"  Area: {cluster['area']:.2f} square units\")\n", "    print(f\"  Tilt angle: {cluster['tilt_angle']:.2f} degrees\")\n", "    print(f\"  Azimuth: {cluster['azimuth']:.2f} degrees\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Save Results\n", "\n", "Finally, let's save the segmented panels and their properties for further analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save the clusters data\n", "print(\"\\n=== Saving Cluster Data ===\\n\")\n", "\n", "# Create a dictionary to store the cluster information\n", "clusters_data = {\n", "    'num_clusters': len(all_clusters),\n", "    'metadata': {\n", "        'coordinate_system': {\n", "            'description': 'Right-handed coordinate system with Z-axis pointing up',\n", "            'x_axis': 'East',\n", "            'y_axis': 'North',\n", "            'z_axis': 'Up',\n", "            'units': 'meters'\n", "        },\n", "        'processing_info': {\n", "            'dbscan_eps': eps,  # Use the last eps value from the loop\n", "            'dbscan_min_samples': min_samples,  # Use the last min_samples value from the loop\n", "            'normal_weight': 0.3,  # Weight used for normal vectors in clustering\n", "            'creation_timestamp': time.strftime('%Y-%m-%d %H:%M:%S')\n", "        }\n", "    },\n", "    'clusters': []\n", "}\n", "\n", "# Add information for each cluster\n", "for cluster in all_clusters:\n", "    # Calculate quality metrics\n", "    # 1. Normal vector consistency (higher is better)\n", "    eigenvalues = cluster.get('eigenvalues', np.array([1, 1, 0.1]))\n", "    planarity = 1.0 - (eigenvalues[2] / (eigenvalues[0] + 1e-10))  # How planar is the cluster\n", "    \n", "    # 2. Point density (points per square meter)\n", "    area = float(cluster['area']) if cluster['area'] > 0 else 1.0\n", "    point_density = cluster['num_points'] / area\n", "    \n", "    # 3. Confidence score (combination of planarity and point density)\n", "    # Normalize point density to 0-1 range (assuming 100 points/m² is good)\n", "    norm_density = min(1.0, point_density / 100.0)\n", "    confidence_score = 0.7 * planarity + 0.3 * norm_density\n", "    \n", "    # Create a copy without the large arrays\n", "    cluster_info = {\n", "        'plane_id': cluster['plane_id'],\n", "        'cluster_id': cluster['cluster_id'],\n", "        'num_points': cluster['num_points'],\n", "        'centroid': cluster['centroid'].tolist(),\n", "        'normal': cluster['normal'].tolist(),\n", "        'length': float(cluster['length']),\n", "        'width': float(cluster['width']),\n", "        'area': float(cluster['area']),\n", "        'tilt_angle': float(cluster['tilt_angle']),\n", "        'azimuth': float(cluster['azimuth']),\n", "        'quality_metrics': {\n", "            'planarity': float(planarity),\n", "            'point_density': float(point_density),\n", "            'confidence_score': float(confidence_score)\n", "        }\n", "    }\n", "    clusters_data['clusters'].append(cluster_info)\n", "\n", "# Save the clusters data as a NumPy file\n", "clusters_file = os.path.join(output_dir, 'segmented_panels.npy')\n", "np.save(clusters_file, clusters_data)\n", "print(f\"Saved segmented panels to {clusters_file}\")\n", "\n", "# Save each cluster's points as a separate PLY file\n", "if O3D_SUPPORT:\n", "    for i, (cluster, points) in enumerate(zip(all_clusters, all_cluster_points)):\n", "        # Create an Open3D point cloud for the cluster\n", "        cluster_pcd = o3d.geometry.PointCloud()\n", "        cluster_pcd.points = o3d.utility.Vector3dVector(points)\n", "        \n", "        # Save the point cloud\n", "        cluster_file = os.path.join(output_dir, f'panel_{i+1}.ply')\n", "        o3d.io.write_point_cloud(cluster_file, cluster_pcd)\n", "        print(f\"Saved panel {i+1} to {cluster_file}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Conclusion\n", "\n", "In this notebook, we've implemented and used DBSCAN clustering to segment detected planes into individual solar panels. We've demonstrated how to:\n", "\n", "1. Load planes detected by RANSAC with preserved original point indices\n", "2. Use our modular DBSCAN implementation from `src.plane_detection.dbscan`\n", "3. Segment planes into individual panels while preserving original point indices\n", "4. Visualize the segmented panels\n", "5. Calculate geometric properties for each panel\n", "6. Save the results with metadata for further analysis\n", "\n", "Key improvements in our implementation:\n", "\n", "- **Modular Implementation**: We've created a reusable DBSCAN module in `src.plane_detection.dbscan`\n", "- **Original Point Indices Preservation**: We now preserve the original point indices throughout the pipeline\n", "- **Metadata Preservation**: We include coordinate system information and other metadata in the output files\n", "- **Improved File Organization**: We save all outputs in a structured directory hierarchy\n", "\n", "This implementation can be used as a foundation for more advanced solar panel analysis tasks, such as:\n", "\n", "- Detecting anomalies in panel orientation\n", "- Measuring spacing between panels\n", "- Identifying missing or damaged panels\n", "- Extracting features for machine learning-based anomaly detection\n", "\n", "Next steps include:\n", "\n", "1. Running the complete pipeline using `src.plane_detection.run_pipeline`\n", "2. Implementing more sophisticated geometric analysis\n", "3. Developing anomaly detection algorithms that leverage the preserved original point indices\n", "4. Integrating with a visualization dashboard"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}