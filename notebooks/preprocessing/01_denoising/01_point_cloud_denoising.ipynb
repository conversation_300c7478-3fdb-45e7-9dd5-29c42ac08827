{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Point Cloud Denoising - Preprocessing Pipeline Stage 1\n", "\n", "**Stage**: Preprocessing - Denoising (First Step)  \n", "**Input Data**: Raw point cloud (.las, .laz files) from data/raw directory  \n", "**Output**: Denoised point clouds saved to data/processed directory  \n", "**Format**: .ply files for downstream ground segmentation methods  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis\n", "\n", "## Overview\n", "\n", "This notebook implements comprehensive point cloud denoising as the **first step** in the preprocessing pipeline. It ensures consistent, noise-free inputs for all downstream ground segmentation methods:\n", "\n", "- **CSF (Cloth Simulation Filter)**\n", "- **PMF (Progressive Morphological Filter)**\n", "- **RANSAC (Random Sample Consensus)**\n", "- **Combined RANSAC + PMF**\n", "\n", "## Denoising Strategy\n", "\n", "1. **Statistical Outlier Removal** - Remove points that deviate significantly from local neighborhoods\n", "2. **Radius Outlier Removal** - Remove isolated points with insufficient neighbors\n", "3. **Voxel Grid Filtering** - Reduce noise through spatial averaging\n", "4. **Conditional Smoothing** - Apply smoothing while preserving structural features\n", "\n", "## Pipeline Integration\n", "\n", "- **Executed across all sites** using Papermill parameterization\n", "- **Consistent preprocessing** ensures fair comparison between ground segmentation methods\n", "- **Quality metrics tracking** via MLflow for monitoring denoising effectiveness"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill Parameters - Override these when running with papermill\n", "project_type = \"foundation_analysis\"  # Project type for data organization\n", "site_name = \"castro_area4\"            # Site identifier for processing\n", "point_cloud_path = None               # Optional: specific point cloud file path\n", "\n", "# Statistical Outlier Removal Parameters\n", "stat_nb_neighbors = 20                # Number of neighbors for statistical analysis\n", "stat_std_ratio = 2.0                  # Standard deviation threshold (higher = more permissive)\n", "\n", "# Radius Outlier Removal Parameters\n", "radius_nb_points = 16                 # Minimum neighbors required within radius\n", "radius_search = 0.5                   # Search radius in meters\n", "\n", "# Voxel Grid Filtering Parameters\n", "voxel_size = 0.05                     # Voxel size in meters (5cm default)\n", "enable_voxel_filtering = True         # Enable/disable voxel grid filtering\n", "\n", "# Processing Parameters\n", "max_points_processing = 5000000       # Maximum points to process (0 = no limit)\n", "downsample_factor = 1.0               # Initial downsampling factor (1.0 = no downsampling)\n", "save_intermediate_results = True      # Save intermediate denoising steps\n", "generate_comparison_plots = True      # Create before/after visualizations\n", "use_mlflow = True                     # Enable MLflow tracking"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import numpy as np\n", "import json\n", "import matplotlib.pyplot as plt\n", "import time\n", "import logging\n", "from pathlib import Path\n", "from datetime import datetime\n", "from tqdm import tqdm\n", "import open3d as o3d\n", "import laspy\n", "import pandas as pd\n", "\n", "# MLflow for experiment tracking\n", "if use_mlflow:\n", "    import mlflow\n", "    import mlflow.sklearn\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"Point Cloud Denoising - Preprocessing Stage 1 - Starting...\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup paths following project data organization\n", "project_root = Path('../..')  # Navigate to project root from notebooks/preprocessing/\n", "data_path = project_root / 'data'\n", "raw_path = data_path / 'raw' / project_type / site_name\n", "processed_path = data_path / 'processed' / project_type / site_name / 'denoising'\n", "output_path = data_path / 'output_runs'\n", "analysis_output_path = data_path / 'analysis_output'\n", "\n", "# Create directories\n", "processed_path.mkdir(parents=True, exist_ok=True)\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "analysis_output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"Project root: {project_root.resolve()}\")\n", "print(f\"Raw data path: {raw_path.resolve()}\")\n", "print(f\"Processed data path: {processed_path.resolve()}\")\n", "print(f\"Output runs path: {output_path.resolve()}\")\n", "print(f\"Analysis output path: {analysis_output_path.resolve()}\")\n", "\n", "# Initialize MLflow if enabled\n", "if use_mlflow:\n", "    mlflow.set_experiment(f\"point_cloud_denoising_{project_type}\")\n", "    mlflow.start_run(run_name=f\"denoising_{site_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}\")\n", "    mlflow.log_param(\"project_type\", project_type)\n", "    mlflow.log_param(\"site_name\", site_name)\n", "    mlflow.log_param(\"stat_nb_neighbors\", stat_nb_neighbors)\n", "    mlflow.log_param(\"stat_std_ratio\", stat_std_ratio)\n", "    mlflow.log_param(\"radius_nb_points\", radius_nb_points)\n", "    mlflow.log_param(\"radius_search\", radius_search)\n", "    mlflow.log_param(\"voxel_size\", voxel_size)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading\n", "\n", "Load point cloud data from LAS files:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load point cloud data\n", "start_time = time.time()\n", "\n", "# Determine input file path\n", "if point_cloud_path:\n", "    input_path = Path(point_cloud_path)\n", "    logger.info(f\"Custom point_cloud_path provided: {input_path}\")\n", "    if not input_path.exists():\n", "        raise FileNotFoundError(f\"Input path does not exist: {input_path}\")\n", "else:\n", "    # Find LAS files in raw data directory\n", "    las_files = list(raw_path.glob('*.las')) + list(raw_path.glob('*.laz'))\n", "    if not las_files:\n", "        raise FileNotFoundError(f\"No LAS files found in {raw_path}\")\n", "    input_path = las_files[0]  # Use first LAS file found\n", "    logger.info(f\"Using LAS file: {input_path}\")\n", "\n", "print(f\"Loading point cloud from: {input_path}\")\n", "\n", "# Load LAS file\n", "las_file = laspy.read(input_path)\n", "points = np.vstack((las_file.x, las_file.y, las_file.z)).transpose()\n", "\n", "# Apply initial downsampling if specified\n", "if downsample_factor < 1.0:\n", "    n_points = int(len(points) * downsample_factor)\n", "    indices = np.random.choice(len(points), n_points, replace=False)\n", "    points = points[indices]\n", "    print(f\"Initial downsampling to {len(points)} points (factor: {downsample_factor})\")\n", "\n", "# Apply max points limit if specified\n", "if max_points_processing > 0 and len(points) > max_points_processing:\n", "    indices = np.random.choice(len(points), max_points_processing, replace=False)\n", "    points = points[indices]\n", "    print(f\"Limited to {len(points)} points for processing\")\n", "\n", "print(f\"Point cloud loaded: {len(points)} points\")\n", "print(f\"Bounds: X[{points[:, 0].min():.2f}, {points[:, 0].max():.2f}], \"\n", "      f\"Y[{points[:, 1].min():.2f}, {points[:, 1].max():.2f}], \"\n", "      f\"Z[{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]\")\n", "\n", "load_time = time.time() - start_time\n", "print(f\"Loading completed in {load_time:.2f} seconds\")\n", "\n", "# Convert to Open3D point cloud\n", "original_pcd = o3d.geometry.PointCloud()\n", "original_pcd.points = o3d.utility.Vector3dVector(points)\n", "\n", "# Log to MLflow\n", "if use_mlflow:\n", "    mlflow.log_metric(\"input_points\", len(points))\n", "    mlflow.log_metric(\"load_time_seconds\", load_time)\n", "    mlflow.log_param(\"input_file\", str(input_path))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Denoising Pipeline\n", "\n", "Apply comprehensive denoising methods:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize denoising pipeline\n", "denoising_results = {}\n", "current_pcd = original_pcd\n", "pipeline_start = time.time()\n", "\n", "print(\"\\nSTARTING DENOISING PIPELINE:\")\n", "print(\"=\" * 50)\n", "print(f\"Original point count: {len(current_pcd.points):,}\")\n", "\n", "# Step 1: Statistical Outlier Removal\n", "print(f\"\\nStep 1: Statistical Outlier Removal\")\n", "print(f\"  Parameters: {stat_nb_neighbors} neighbors, {stat_std_ratio} std ratio\")\n", "\n", "step1_start = time.time()\n", "stat_filtered_pcd, stat_outlier_indices = current_pcd.remove_statistical_outlier(\n", "    nb_neighbors=stat_nb_neighbors,\n", "    std_ratio=stat_std_ratio\n", ")\n", "step1_time = time.time() - step1_start\n", "\n", "stat_outliers_removed = len(current_pcd.points) - len(stat_filtered_pcd.points)\n", "stat_removal_ratio = stat_outliers_removed / len(current_pcd.points)\n", "\n", "print(f\"  Outliers removed: {stat_outliers_removed:,} ({stat_removal_ratio*100:.2f}%)\")\n", "print(f\"  Remaining points: {len(stat_filtered_pcd.points):,}\")\n", "print(f\"  Processing time: {step1_time:.2f} seconds\")\n", "\n", "denoising_results['statistical_outlier_removal'] = {\n", "    'outliers_removed': stat_outliers_removed,\n", "    'removal_ratio': stat_removal_ratio,\n", "    'remaining_points': len(stat_filtered_pcd.points),\n", "    'processing_time': step1_time\n", "}\n", "\n", "# Save intermediate result if requested\n", "if save_intermediate_results:\n", "    step1_path = processed_path / f\"{site_name}_step1_statistical_filtered.ply\"\n", "    o3d.io.write_point_cloud(str(step1_path), stat_filtered_pcd)\n", "    print(f\"  Intermediate result saved: {step1_path}\")\n", "\n", "current_pcd = stat_filtered_pcd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 2: <PERSON><PERSON> Outlier Removal\n", "print(f\"\\nStep 2: Radius Outlier Removal\")\n", "print(f\"  Parameters: {radius_nb_points} min neighbors, {radius_search}m radius\")\n", "\n", "step2_start = time.time()\n", "radius_filtered_pcd, radius_outlier_indices = current_pcd.remove_radius_outlier(\n", "    nb_points=radius_nb_points,\n", "    radius=radius_search\n", ")\n", "step2_time = time.time() - step2_start\n", "\n", "radius_outliers_removed = len(current_pcd.points) - len(radius_filtered_pcd.points)\n", "radius_removal_ratio = radius_outliers_removed / len(current_pcd.points)\n", "\n", "print(f\"  Outliers removed: {radius_outliers_removed:,} ({radius_removal_ratio*100:.2f}%)\")\n", "print(f\"  Remaining points: {len(radius_filtered_pcd.points):,}\")\n", "print(f\"  Processing time: {step2_time:.2f} seconds\")\n", "\n", "denoising_results['radius_outlier_removal'] = {\n", "    'outliers_removed': radius_outliers_removed,\n", "    'removal_ratio': radius_removal_ratio,\n", "    'remaining_points': len(radius_filtered_pcd.points),\n", "    'processing_time': step2_time\n", "}\n", "\n", "# Save intermediate result if requested\n", "if save_intermediate_results:\n", "    step2_path = processed_path / f\"{site_name}_step2_radius_filtered.ply\"\n", "    o3d.io.write_point_cloud(str(step2_path), radius_filtered_pcd)\n", "    print(f\"  Intermediate result saved: {step2_path}\")\n", "\n", "current_pcd = radius_filtered_pcd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 3: Voxel Grid Filtering (Optional)\n", "if enable_voxel_filtering:\n", "    print(f\"\\nStep 3: Voxel Grid Filtering\")\n", "    print(f\"  Parameters: {voxel_size}m voxel size\")\n", "    \n", "    step3_start = time.time()\n", "    voxel_filtered_pcd = current_pcd.voxel_down_sample(voxel_size=voxel_size)\n", "    step3_time = time.time() - step3_start\n", "    \n", "    voxel_points_removed = len(current_pcd.points) - len(voxel_filtered_pcd.points)\n", "    voxel_reduction_ratio = voxel_points_removed / len(current_pcd.points)\n", "    \n", "    print(f\"  Points reduced: {voxel_points_removed:,} ({voxel_reduction_ratio*100:.2f}%)\")\n", "    print(f\"  Remaining points: {len(voxel_filtered_pcd.points):,}\")\n", "    print(f\"  Processing time: {step3_time:.2f} seconds\")\n", "    \n", "    denoising_results['voxel_grid_filtering'] = {\n", "        'points_reduced': voxel_points_removed,\n", "        'reduction_ratio': voxel_reduction_ratio,\n", "        'remaining_points': len(voxel_filtered_pcd.points),\n", "        'processing_time': step3_time\n", "    }\n", "    \n", "    # Save intermediate result if requested\n", "    if save_intermediate_results:\n", "        step3_path = processed_path / f\"{site_name}_step3_voxel_filtered.ply\"\n", "        o3d.io.write_point_cloud(str(step3_path), voxel_filtered_pcd)\n", "        print(f\"  Intermediate result saved: {step3_path}\")\n", "    \n", "    current_pcd = voxel_filtered_pcd\n", "else:\n", "    print(f\"\\nStep 3: Voxel Grid Filtering - SKIPPED\")\n", "    denoising_results['voxel_grid_filtering'] = {'skipped': True}\n", "\n", "# Final denoised point cloud\n", "final_pcd = current_pcd\n", "pipeline_time = time.time() - pipeline_start\n", "\n", "print(f\"\\nDENOISING PIPELINE COMPLETED:\")\n", "print(\"=\" * 50)\n", "print(f\"Original points: {len(original_pcd.points):,}\")\n", "print(f\"Final points: {len(final_pcd.points):,}\")\n", "total_removed = len(original_pcd.points) - len(final_pcd.points)\n", "total_removal_ratio = total_removed / len(original_pcd.points)\n", "print(f\"Total removed: {total_removed:,} ({total_removal_ratio*100:.2f}%)\")\n", "print(f\"Total pipeline time: {pipeline_time:.2f} seconds\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Quality Analysis and Visualization\n", "\n", "Analyze denoising effectiveness and generate comparison plots:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate quality metrics\n", "original_points = np.asarray(original_pcd.points)\n", "final_points = np.asarray(final_pcd.points)\n", "\n", "# Point density analysis\n", "original_bounds = {\n", "    'x_range': original_points[:, 0].max() - original_points[:, 0].min(),\n", "    'y_range': original_points[:, 1].max() - original_points[:, 1].min(),\n", "    'z_range': original_points[:, 2].max() - original_points[:, 2].min()\n", "}\n", "\n", "final_bounds = {\n", "    'x_range': final_points[:, 0].max() - final_points[:, 0].min(),\n", "    'y_range': final_points[:, 1].max() - final_points[:, 1].min(),\n", "    'z_range': final_points[:, 2].max() - final_points[:, 2].min()\n", "}\n", "\n", "# Calculate approximate area and density\n", "original_area = original_bounds['x_range'] * original_bounds['y_range']\n", "final_area = final_bounds['x_range'] * final_bounds['y_range']\n", "original_density = len(original_points) / original_area if original_area > 0 else 0\n", "final_density = len(final_points) / final_area if final_area > 0 else 0\n", "\n", "# Height statistics\n", "original_z_stats = {\n", "    'mean': float(np.mean(original_points[:, 2])),\n", "    'std': float(np.std(original_points[:, 2])),\n", "    'min': float(np.min(original_points[:, 2])),\n", "    'max': float(np.max(original_points[:, 2]))\n", "}\n", "\n", "final_z_stats = {\n", "    'mean': float(np.mean(final_points[:, 2])),\n", "    'std': float(np.std(final_points[:, 2])),\n", "    'min': float(np.min(final_points[:, 2])),\n", "    'max': float(np.max(final_points[:, 2]))\n", "}\n", "\n", "print(f\"\\nQUALITY ANALYSIS:\")\n", "print(\"=\" * 50)\n", "print(f\"Point Density:\")\n", "print(f\"  Original: {original_density:.2f} points/m²\")\n", "print(f\"  Final: {final_density:.2f} points/m²\")\n", "print(f\"  Density change: {((final_density - original_density) / original_density * 100):.1f}%\")\n", "\n", "print(f\"\\nHeight Statistics:\")\n", "print(f\"  Original Z std: {original_z_stats['std']:.3f}m\")\n", "print(f\"  Final Z std: {final_z_stats['std']:.3f}m\")\n", "print(f\"  Noise reduction: {((original_z_stats['std'] - final_z_stats['std']) / original_z_stats['std'] * 100):.1f}%\")\n", "\n", "# Store comprehensive results\n", "denoising_summary = {\n", "    'processing_timestamp': datetime.now().isoformat(),\n", "    'site_name': site_name,\n", "    'project_type': project_type,\n", "    'input_file': str(input_path),\n", "    'original_points': len(original_points),\n", "    'final_points': len(final_points),\n", "    'total_removed': total_removed,\n", "    'total_removal_ratio': total_removal_ratio,\n", "    'pipeline_time_seconds': pipeline_time,\n", "    'original_density_per_m2': original_density,\n", "    'final_density_per_m2': final_density,\n", "    'original_z_stats': original_z_stats,\n", "    'final_z_stats': final_z_stats,\n", "    'denoising_steps': denoising_results\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate comparison visualizations\n", "if generate_comparison_plots:\n", "    fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "    \n", "    # Sample points for visualization if too many\n", "    viz_sample_size = 50000\n", "    if len(original_points) > viz_sample_size:\n", "        orig_indices = np.random.choice(len(original_points), viz_sample_size, replace=False)\n", "        orig_viz_points = original_points[orig_indices]\n", "    else:\n", "        orig_viz_points = original_points\n", "    \n", "    if len(final_points) > viz_sample_size:\n", "        final_indices = np.random.choice(len(final_points), viz_sample_size, replace=False)\n", "        final_viz_points = final_points[final_indices]\n", "    else:\n", "        final_viz_points = final_points\n", "    \n", "    # Original point cloud (top view)\n", "    axes[0, 0].scatter(orig_viz_points[:, 0], orig_viz_points[:, 1], \n", "                      c=orig_viz_points[:, 2], cmap='terrain', s=0.1, alpha=0.6)\n", "    axes[0, 0].set_title(f'Original Point Cloud\\n{len(original_points):,} points')\n", "    axes[0, 0].set_xlabel('X (m)')\n", "    axes[0, 0].set_ylabel('Y (m)')\n", "    axes[0, 0].axis('equal')\n", "    \n", "    # Denoised point cloud (top view)\n", "    axes[0, 1].scatter(final_viz_points[:, 0], final_viz_points[:, 1], \n", "                      c=final_viz_points[:, 2], cmap='terrain', s=0.1, alpha=0.6)\n", "    axes[0, 1].set_title(f'Denoised Point Cloud\\n{len(final_points):,} points')\n", "    axes[0, 1].set_xlabel('X (m)')\n", "    axes[0, 1].set_ylabel('Y (m)')\n", "    axes[0, 1].axis('equal')\n", "    \n", "    # Height distribution comparison\n", "    axes[0, 2].hist(original_points[:, 2], bins=50, alpha=0.5, label='Original', density=True)\n", "    axes[0, 2].hist(final_points[:, 2], bins=50, alpha=0.5, label='Denoised', density=True)\n", "    axes[0, 2].set_title('Height Distribution Comparison')\n", "    axes[0, 2].set_xlabel('Z (m)')\n", "    axes[0, 2].set_ylabel('Density')\n", "    axes[0, 2].legend()\n", "    \n", "    # Denoising pipeline summary\n", "    steps = ['Original', '<PERSON>at Filter', 'Ra<PERSON>lter']\n", "    point_counts = [len(original_points)]\n", "    \n", "    if 'statistical_outlier_removal' in denoising_results:\n", "        point_counts.append(denoising_results['statistical_outlier_removal']['remaining_points'])\n", "    if 'radius_outlier_removal' in denoising_results:\n", "        point_counts.append(denoising_results['radius_outlier_removal']['remaining_points'])\n", "    \n", "    if enable_voxel_filtering and 'voxel_grid_filtering' in denoising_results:\n", "        steps.append('<PERSON><PERSON><PERSON> Filter')\n", "        point_counts.append(denoising_results['voxel_grid_filtering']['remaining_points'])\n", "    \n", "    axes[1, 0].plot(steps, point_counts, 'o-', linewidth=2, markersize=8)\n", "    axes[1, 0].set_title('Denoising Pipeline Progress')\n", "    axes[1, 0].set_ylabel('Point Count')\n", "    axes[1, 0].tick_params(axis='x', rotation=45)\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    \n", "    # Processing time breakdown\n", "    time_labels = []\n", "    time_values = []\n", "    \n", "    if 'statistical_outlier_removal' in denoising_results:\n", "        time_labels.append('Statistical')\n", "        time_values.append(denoising_results['statistical_outlier_removal']['processing_time'])\n", "    if 'radius_outlier_removal' in denoising_results:\n", "        time_labels.append('Radius')\n", "        time_values.append(denoising_results['radius_outlier_removal']['processing_time'])\n", "    if enable_voxel_filtering and 'voxel_grid_filtering' in denoising_results:\n", "        time_labels.append('Voxel')\n", "        time_values.append(denoising_results['voxel_grid_filtering']['processing_time'])\n", "    \n", "    axes[1, 1].bar(time_labels, time_values)\n", "    axes[1, 1].set_title('Processing Time by Step')\n", "    axes[1, 1].set_ylabel('Time (seconds)')\n", "    axes[1, 1].tick_params(axis='x', rotation=45)\n", "    \n", "    # Summary statistics\n", "    summary_text = f\"\"\"DENOISING SUMMARY\n", "    \n", "Original Points: {len(original_points):,}\n", "Final Points: {len(final_points):,}\n", "Removed: {total_removed:,} ({total_removal_ratio*100:.1f}%)\n", "\n", "Processing Time: {pipeline_time:.1f}s\n", "Density Change: {((final_density - original_density) / original_density * 100):.1f}%\n", "Z Std Reduction: {((original_z_stats['std'] - final_z_stats['std']) / original_z_stats['std'] * 100):.1f}%\n", "\n", "Ready for Ground Segmentation:\n", "✓ CSF, PMF, RANSAC, Combined\"\"\"\n", "    \n", "    axes[1, 2].text(0.05, 0.95, summary_text, transform=axes[1, 2].transAxes, \n", "                    fontsize=10, verticalalignment='top', fontfamily='monospace')\n", "    axes[1, 2].set_xlim(0, 1)\n", "    axes[1, 2].set_ylim(0, 1)\n", "    axes[1, 2].axis('off')\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # Save visualization\n", "    viz_path = output_path / f\"{site_name}_denoising_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png\"\n", "    plt.savefig(viz_path, dpi=300, bbox_inches='tight')\n", "    print(f\"\\nVisualization saved: {viz_path}\")\n", "    \n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Output Generation\n", "\n", "Save denoised point cloud and analysis results:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save final denoised point cloud\n", "final_output_path = processed_path / f\"{site_name}_denoised.ply\"\n", "o3d.io.write_point_cloud(str(final_output_path), final_pcd)\n", "print(f\"\\nFinal denoised point cloud saved: {final_output_path}\")\n", "\n", "# Save analysis results to output_runs\n", "results_path = output_path / f\"{site_name}_denoising_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json\"\n", "with open(results_path, 'w') as f:\n", "    json.dump(denoising_summary, f, indent=2)\n", "print(f\"Denoising results saved: {results_path}\")\n", "\n", "# Save analysis summary to analysis_output\n", "analysis_summary_path = analysis_output_path / f\"{site_name}_denoising_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json\"\n", "with open(analysis_summary_path, 'w') as f:\n", "    json.dump({\n", "        'site_name': site_name,\n", "        'processing_date': datetime.now().isoformat(),\n", "        'noise_reduction_effectiveness': {\n", "            'points_removed': total_removed,\n", "            'removal_percentage': total_removal_ratio * 100,\n", "            'z_std_reduction_percentage': ((original_z_stats['std'] - final_z_stats['std']) / original_z_stats['std'] * 100),\n", "            'density_change_percentage': ((final_density - original_density) / original_density * 100)\n", "        },\n", "        'ready_for_ground_segmentation': True,\n", "        'output_file': str(final_output_path)\n", "    }, f, indent=2)\n", "print(f\"Analysis summary saved: {analysis_summary_path}\")\n", "\n", "# Log comprehensive metrics to MLflow\n", "if use_mlflow:\n", "    mlflow.log_metric(\"final_points\", len(final_points))\n", "    mlflow.log_metric(\"total_removed\", total_removed)\n", "    mlflow.log_metric(\"removal_ratio\", total_removal_ratio)\n", "    mlflow.log_metric(\"pipeline_time_seconds\", pipeline_time)\n", "    mlflow.log_metric(\"original_density_per_m2\", original_density)\n", "    mlflow.log_metric(\"final_density_per_m2\", final_density)\n", "    mlflow.log_metric(\"z_std_reduction\", (original_z_stats['std'] - final_z_stats['std']) / original_z_stats['std'])\n", "    \n", "    # Log individual step metrics\n", "    for step_name, step_results in denoising_results.items():\n", "        if isinstance(step_results, dict) and 'processing_time' in step_results:\n", "            mlflow.log_metric(f\"{step_name}_time\", step_results['processing_time'])\n", "            if 'removal_ratio' in step_results:\n", "                mlflow.log_metric(f\"{step_name}_removal_ratio\", step_results['removal_ratio'])\n", "    \n", "    # Log output files\n", "    mlflow.log_artifact(str(final_output_path), \"denoised_point_clouds\")\n", "    mlflow.log_artifact(str(results_path), \"results\")\n", "    if generate_comparison_plots:\n", "        mlflow.log_artifact(str(viz_path), \"visualizations\")\n", "    \n", "    mlflow.end_run()\n", "\n", "print(f\"\\nDENOISING PIPELINE COMPLETED SUCCESSFULLY\")\n", "print(\"=\" * 50)\n", "print(f\"Site: {site_name}\")\n", "print(f\"Input: {len(original_points):,} points\")\n", "print(f\"Output: {len(final_points):,} points\")\n", "print(f\"Noise removed: {total_removed:,} points ({total_removal_ratio*100:.1f}%)\")\n", "print(f\"Processing time: {pipeline_time:.1f} seconds\")\n", "print(f\"\\nReady for ground segmentation methods:\")\n", "print(f\"  ✓ CSF (Cloth Simulation Filter)\")\n", "print(f\"  ✓ PMF (Progressive Morphological Filter)\")\n", "print(f\"  ✓ RANSAC (Random Sample Consensus)\")\n", "print(f\"  ✓ Combined RANSAC + PMF\")\n", "print(f\"\\nOutput file: {final_output_path}\")\n", "print(f\"Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}