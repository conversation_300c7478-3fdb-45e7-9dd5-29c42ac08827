{"cells": [{"cell_type": "markdown", "metadata": {"id": "EDRF4Jd2DyNO"}, "source": ["# Point Cloud Data Quality Assessment\n", "\n", "## Overview\n", "\n", "This notebook performs comprehensive quality checks on point cloud data (.las/.laz files) to ensure data integrity and suitability for analysis.\n", "\n", "## Quality Checks Performed\n", "\n", "**Point Density** - Points per square meter assessment\n", "\n", "**Coordinate System Validation** - EPSG code and CRS verification\n", "\n", "**Spatial Extent Consistency** - Bounding box validation\n", "\n", "**Orientation & Ground Level** - Elevation and outlier detection\n", "\n", "**File Integrity** - Corruption and format validation\n", "\n", "**Attribute Channels** - Intensity, RGB, and other data presence"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fWfs4Fx2DyNR", "outputId": "3d8a9841-a433-4527-c478-3e01df12a7ac"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: laspy in /usr/local/lib/python3.11/dist-packages (2.5.4)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.11/dist-packages (2.2.2)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (2.0.2)\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.11/dist-packages (3.10.0)\n", "Requirement already satisfied: seaborn in /usr/local/lib/python3.11/dist-packages (0.13.2)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas) (2025.2)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (4.58.1)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (1.4.8)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (24.2)\n", "Requirement already satisfied: pillow>=8 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (11.2.1)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (3.2.3)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->pandas) (1.17.0)\n"]}], "source": ["# Install required packages\n", "!pip install laspy pandas numpy matp<PERSON>lib seaborn"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "H-crbla1e-6j"}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1GiG1RlPDyNS", "outputId": "8758fbbd-d0be-418d-fc12-d691e6abe769"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["laspy available\n"]}], "source": ["try:\n", "    import laspy\n", "    LASPY_AVAILABLE = True\n", "    print(\"laspy available\")\n", "except ImportError:\n", "    LASPY_AVAILABLE = False\n", "    print(\"laspy not available - install with: pip install laspy\")"]}, {"cell_type": "markdown", "metadata": {"id": "VHFCZ06VJCEN"}, "source": ["## LAS/LAZ Processing Functions\n", "\n", "All core functions for processing and analyzing LAS/LAZ files are organized here for better modularity and reusability."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Point Density Analysis Functions"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "9OKqkEMXJCEO"}, "outputs": [], "source": ["import laspy\n", "from dataclasses import dataclass, field\n", "from typing import Optional, Dict, Union\n", "\n", "@dataclass\n", "class DensityReport:\n", "    file_path: str\n", "    num_points: Optional[int] = None\n", "    area_m2: Optional[float] = None\n", "    density_pts_per_m2: Optional[float] = None\n", "    x_range_m: Optional[float] = None\n", "    y_range_m: Optional[float] = None\n", "    status: str = 'success'\n", "    error: Optional[str] = None\n", "\n", "def read_las_file_density(las_file_path: str) -> Union[laspy.LasData, str]:\n", "    try:\n", "        return laspy.read(las_file_path), None\n", "    except Exception as e:\n", "        return None, str(e)\n", "\n", "def compute_xy_range(header: laspy.LasHeader) -> Dict[str, float]:\n", "    x_range = header.max[0] - header.min[0]\n", "    y_range = header.max[1] - header.min[1]\n", "    return {'x_range': x_range, 'y_range': y_range}\n", "\n", "def compute_density(num_points: int, area: float) -> float:\n", "    return num_points / area if area > 0 else 0.0\n", "\n", "def analyze_point_density(las_file_path: str) -> DensityReport:\n", "    las, error = read_las_file_density(las_file_path)\n", "    if error:\n", "        return DensityReport(file_path=las_file_path, status='error', error=error)\n", "\n", "    num_points = len(las.points)\n", "    ranges = compute_xy_range(las.header)\n", "    area_m2 = ranges['x_range'] * ranges['y_range']\n", "    density = compute_density(num_points, area_m2)\n", "\n", "    return DensityReport(\n", "        file_path=las_file_path,\n", "        num_points=num_points,\n", "        area_m2=area_m2,\n", "        density_pts_per_m2=density,\n", "        x_range_m=ranges['x_range'],\n", "        y_range_m=ranges['y_range']\n", "    )\n"]}, {"cell_type": "markdown", "metadata": {"id": "Kl_qQFM6JCEO"}, "source": ["### Coordinate System Validation Functions"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "pbCQ0zpaJCEO"}, "outputs": [], "source": ["from dataclasses import dataclass, field\n", "from typing import Optional, Tuple, List, Union\n", "import laspy\n", "\n", "@dataclass\n", "class CoordinateSystemReport:\n", "    file_path: str\n", "    las_version: Optional[str] = None\n", "    point_format: Optional[int] = None\n", "    crs: Optional[str] = None\n", "    epsg_code: Optional[Union[int, str]] = None\n", "    x_range: Optional[List[float]] = None\n", "    y_range: Optional[List[float]] = None\n", "    z_range: Optional[List[float]] = None\n", "    coord_range_reasonable: Optional[bool] = None\n", "    status: str = 'success'\n", "    error: Optional[str] = None\n", "\n", "def read_las_file_crs(las_file_path: str) -> Union[laspy.LasData, str]:\n", "    try:\n", "        return laspy.read(las_file_path), None\n", "    except Exception as e:\n", "        return None, str(e)\n", "\n", "def extract_crs_info(header: laspy.LasHeader) -> Tuple[Optional[str], Optional[int]]:\n", "    try:\n", "        if hasattr(header, 'crs') and header.crs:\n", "            return str(header.crs), header.crs.to_epsg()\n", "        else:\n", "            return 'Not available', None\n", "    except Exception:\n", "        return 'Error reading CRS', None\n", "\n", "def compute_coord_ranges(header: laspy.LasHeader) -> Tuple[List[float], List[float], List[float], bool]:\n", "    x_range = [header.min[0], header.max[0]]\n", "    y_range = [header.min[1], header.max[1]]\n", "    z_range = [header.min[2], header.max[2]]\n", "\n", "    coord_ok = (\n", "        abs(x_range[1] - x_range[0]) < 1e6 and\n", "        abs(y_range[1] - y_range[0]) < 1e6 and\n", "        abs(z_range[1] - z_range[0]) < 1e4\n", "    )\n", "    return x_range, y_range, z_range, coord_ok\n", "\n", "def validate_coordinate_system(las_file_path: str) -> CoordinateSystemReport:\n", "    las, error = read_las_file_crs(las_file_path)\n", "    if error:\n", "        return CoordinateSystemReport(file_path=las_file_path, status='error', error=error)\n", "\n", "    header = las.header\n", "    crs_str, epsg_code = extract_crs_info(header)\n", "    x_range, y_range, z_range, coord_ok = compute_coord_ranges(header)\n", "\n", "    return CoordinateSystemReport(\n", "        file_path=las_file_path,\n", "        las_version=str(header.version),\n", "        point_format=header.point_format.id,\n", "        crs=crs_str,\n", "        epsg_code=epsg_code,\n", "        x_range=x_range,\n", "        y_range=y_range,\n", "        z_range=z_range,\n", "        coord_range_reasonable=coord_ok\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "iRa7T5_9JCEO"}, "source": ["### Spatial Extent & Bounding Box Functions"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "aSlYK21XJCEO"}, "outputs": [], "source": ["def check_spatial_extent(las_file_path, expected_bounds=None):\n", "    \"\"\"\n", "    Check spatial extent and validate against expected bounds if provided.\n", "    \"\"\"\n", "    try:\n", "        las = laspy.read(las_file_path)\n", "\n", "        # Get actual bounds\n", "        actual_bounds = {\n", "            'x_min': las.header.min[0],\n", "            'x_max': las.header.max[0],\n", "            'y_min': las.header.min[1],\n", "            'y_max': las.header.max[1],\n", "            'z_min': las.header.min[2],\n", "            'z_max': las.header.max[2]\n", "        }\n", "\n", "        result = {\n", "            'file_path': las_file_path,\n", "            'bounds': actual_bounds,\n", "            'status': 'success'\n", "        }\n", "\n", "        # Compare with expected bounds if provided\n", "        if expected_bounds:\n", "            bounds_check = all([\n", "                expected_bounds['x_min'] <= actual_bounds['x_min'] <= expected_bounds['x_max'],\n", "                expected_bounds['y_min'] <= actual_bounds['y_min'] <= expected_bounds['y_max'],\n", "                actual_bounds['x_max'] <= expected_bounds['x_max'],\n", "                actual_bounds['y_max'] <= expected_bounds['y_max']\n", "            ])\n", "            result['bounds_within_expected'] = bounds_check\n", "            result['expected_bounds'] = expected_bounds\n", "\n", "        return result\n", "\n", "    except Exception as e:\n", "        return {\n", "            'file_path': las_file_path,\n", "            'error': str(e),\n", "            'status': 'error'\n", "        }"]}, {"cell_type": "markdown", "metadata": {"id": "XIP7BhWJJCEO"}, "source": ["### Orientation & Ground Level Check Functions"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "dHo9iAfWJCEP"}, "outputs": [], "source": ["from dataclasses import dataclass\n", "from typing import Optional, Dict, Union\n", "import laspy\n", "\n", "@dataclass\n", "class Bounds:\n", "    x_min: float\n", "    x_max: float\n", "    y_min: float\n", "    y_max: float\n", "    z_min: float\n", "    z_max: float\n", "\n", "@dataclass\n", "class SpatialExtentReport:\n", "    file_path: str\n", "    bounds: Optional[Bounds] = None\n", "    expected_bounds: Optional[Dict[str, float]] = None\n", "    bounds_within_expected: Optional[bool] = None\n", "    status: str = 'success'\n", "    error: Optional[str] = None\n", "\n", "def extract_bounds(header: laspy.LasHeader) -> Bounds:\n", "    return Bounds(\n", "        x_min=header.min[0], x_max=header.max[0],\n", "        y_min=header.min[1], y_max=header.max[1],\n", "        z_min=header.min[2], z_max=header.max[2]\n", "    )\n", "\n", "def is_within_expected(actual: Bounds, expected: Dict[str, float]) -> bool:\n", "    return all([\n", "        expected['x_min'] <= actual.x_min <= expected['x_max'],\n", "        expected['y_min'] <= actual.y_min <= expected['y_max'],\n", "        actual.x_max <= expected['x_max'],\n", "        actual.y_max <= expected['y_max']\n", "    ])\n", "\n", "def check_spatial_extent(las_file_path: str, expected_bounds: Optional[Dict[str, float]] = None) -> SpatialExtentReport:\n", "    try:\n", "        las = laspy.read(las_file_path)\n", "        actual_bounds = extract_bounds(las.header)\n", "\n", "        report = SpatialExtentReport(\n", "            file_path=las_file_path,\n", "            bounds=actual_bounds\n", "        )\n", "\n", "        if expected_bounds:\n", "            report.expected_bounds = expected_bounds\n", "            report.bounds_within_expected = is_within_expected(actual_bounds, expected_bounds)\n", "\n", "        return report\n", "\n", "    except Exception as e:\n", "        return SpatialExtentReport(\n", "            file_path=las_file_path,\n", "            status='error',\n", "            error=str(e)\n", "        )"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "XKzaifSdgsMk"}, "outputs": [], "source": ["from dataclasses import dataclass\n", "from typing import Optional, List\n", "import numpy as np\n", "import laspy\n", "\n", "@dataclass\n", "class OrientationReport:\n", "    file_path: str\n", "    z_statistics: Optional[dict] = None\n", "    potential_issues: Optional[List[str]] = None\n", "    status: str = 'success'\n", "    error: Optional[str] = None\n", "\n", "def check_orientation_and_ground(las_file_path: str) -> OrientationReport:\n", "    try:\n", "        las = laspy.read(las_file_path)\n", "        z_values = las.z\n", "\n", "        z_min = float(np.min(z_values))\n", "        z_max = float(np.max(z_values))\n", "        z_range = float(z_max - z_min)\n", "        z_mean = float(np.mean(z_values))\n", "\n", "        potential_issues = []\n", "        if z_range > 100:  # example heuristic\n", "            potential_issues.append(\"Large vertical range; might need normalization or classification.\")\n", "\n", "        return OrientationReport(\n", "            file_path=las_file_path,\n", "            z_statistics={\n", "                'z_min': z_min,\n", "                'z_max': z_max,\n", "                'z_range': z_range,\n", "                'z_mean': z_mean\n", "            },\n", "            potential_issues=potential_issues\n", "        )\n", "\n", "    except Exception as e:\n", "        return OrientationReport(\n", "            file_path=las_file_path,\n", "            status='error',\n", "            error=str(e)\n", "        )\n"]}, {"cell_type": "markdown", "metadata": {"id": "GM4Z2usjJCEP"}, "source": ["### File Integrity Check Functions"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "_2F1r-7NJCEP"}, "outputs": [], "source": ["import os\n", "import laspy\n", "from dataclasses import dataclass, field\n", "from typing import Optional, Dict, Union\n", "\n", "@dataclass\n", "class LASIntegrityReport:\n", "    file_path: str\n", "    integrity_checks: Dict[str, Union[bool, int, float, str]] = field(default_factory=dict)\n", "    status: str = \"success\"\n", "    error: Optional[str] = None\n", "\n", "def file_exists_and_size(las_file_path: str) -> Union[int, str]:\n", "    if not os.path.exists(las_file_path):\n", "        return None, \"File not found\"\n", "    return os.path.getsize(las_file_path), None\n", "\n", "def read_las_header(las_file_path: str) -> Union[laspy.LasHeader, str]:\n", "    try:\n", "        las = laspy.read(las_file_path)\n", "        return las.header, None, las\n", "    except Exception as e:\n", "        return None, str(e), None\n", "\n", "def perform_integrity_checks(las: laspy.LasData, header: laspy.LasHeader, file_size: int) -> Dict:\n", "    checks = {\n", "        'file_readable': True,\n", "        'file_size_bytes': file_size,\n", "        'file_size_mb': round(file_size / (1024 * 1024), 2),\n", "        'header_valid': True,\n", "        'point_count_match': len(las.points) == header.point_count,\n", "        'las_version': str(header.version),\n", "        #'las_version': f\"{header.version_major}.{header.version_minor}\",\n", "        'point_format': header.point_format.id\n", "    }\n", "    if file_size < 1024:\n", "        checks['warning'] = 'File suspiciously small'\n", "    return checks\n", "\n", "def check_file_integrity(las_file_path: str) -> LASIntegrityReport:\n", "    file_size, size_error = file_exists_and_size(las_file_path)\n", "    if size_error:\n", "        return LASIntegrityReport(\n", "            file_path=las_file_path,\n", "            status='error',\n", "            error=size_error\n", "        )\n", "\n", "    header, read_error, las = read_las_header(las_file_path)\n", "    if read_error:\n", "        return LASIntegrityReport(\n", "            file_path=las_file_path,\n", "            status='error',\n", "            error=read_error\n", "        )\n", "\n", "    checks = perform_integrity_checks(las, header, file_size)\n", "\n", "    return LASIntegrityReport(\n", "        file_path=las_file_path,\n", "        integrity_checks=checks\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "rW792IeoJCEP"}, "source": ["### Intensity and Color Channels Check Functions"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "7XKPHbEVJCEP"}, "outputs": [], "source": ["from dataclasses import dataclass, field\n", "from typing import List, Dict, Optional\n", "\n", "import laspy\n", "import numpy as np\n", "from dataclasses import dataclass, field\n", "from typing import List, Dict, Union, Optional\n", "\n", "@dataclass\n", "class LASAttributeReport:\n", "    file_path: str\n", "    available_attributes: List[str] = field(default_factory=list)\n", "    attribute_checks: Dict[str, bool] = field(default_factory=dict)\n", "    attribute_statistics: Dict[str, Dict] = field(default_factory=dict)\n", "    point_format_id: Optional[int] = None\n", "    status: str = \"success\"\n", "    error: Optional[str] = None\n", "\n", "def read_las_file(las_file_path: str) -> Union[laspy.LasData, str]:\n", "    try:\n", "        las = laspy.read(las_file_path)\n", "        return las, None\n", "    except Exception as e:\n", "        return None, str(e)\n", "\n", "def get_available_attributes(las: laspy.LasData) -> List[str]:\n", "    return list(las.point_format.dimension_names)\n", "\n", "def check_standard_attributes(available_attrs: List[str]) -> Dict[str, bool]:\n", "    return {\n", "        'has_intensity': 'intensity' in available_attrs,\n", "        'has_rgb': all(attr in available_attrs for attr in ['red', 'green', 'blue']),\n", "        'has_classification': 'classification' in available_attrs,\n", "        'has_return_info': 'return_number' in available_attrs,\n", "        'has_gps_time': 'gps_time' in available_attrs,\n", "        'has_scan_angle': 'scan_angle_rank' in available_attrs\n", "    }\n", "\n", "def compute_intensity_stats(intensity_data: np.ndarray) -> Dict:\n", "    return {\n", "        'min': int(np.min(intensity_data)),\n", "        'max': int(np.max(intensity_data)),\n", "        'mean': float(np.mean(intensity_data)),\n", "        'non_zero_count': int(np.count_nonzero(intensity_data))\n", "    }\n", "\n", "def compute_rgb_stats(las: laspy.LasData) -> Dict:\n", "    return {\n", "        'red_range': [int(np.min(las.red)), int(np.max(las.red))],\n", "        'green_range': [int(np.min(las.green)), int(np.max(las.green))],\n", "        'blue_range': [int(np.min(las.blue)), int(np.max(las.blue))]\n", "    }\n", "\n", "def gather_attribute_statistics(las: laspy.LasData, attr_checks: Dict[str, bool]) -> Dict[str, Dict]:\n", "    stats = {}\n", "    if attr_checks.get('has_intensity'):\n", "        stats['intensity'] = compute_intensity_stats(las.intensity)\n", "    if attr_checks.get('has_rgb'):\n", "        stats['rgb'] = compute_rgb_stats(las)\n", "    return stats\n", "\n", "def check_attribute_channels(las_file_path: str) -> LASAttributeReport:\n", "    las, error = read_las_file(las_file_path)\n", "    if error:\n", "        return LASAttributeReport(\n", "            file_path=las_file_path,\n", "            status='error',\n", "            error=error\n", "        )\n", "\n", "    available_attrs = get_available_attributes(las)\n", "    attr_checks = check_standard_attributes(available_attrs)\n", "    attr_stats = gather_attribute_statistics(las, attr_checks)\n", "\n", "    return LASAttributeReport(\n", "        file_path=las_file_path,\n", "        available_attributes=available_attrs,\n", "        attribute_checks=attr_checks,\n", "        attribute_statistics=attr_stats,\n", "        point_format_id=las.header.point_format.id\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "fwR-Tn1wJCEP"}, "source": ["### Comprehensive Quality Assessment Function"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "GsNlrnghJCEP"}, "outputs": [], "source": ["def comprehensive_quality_check(las_file_path, expected_bounds=None):\n", "    \"\"\"\n", "    Run all quality checks on a single LAS file.\n", "    \"\"\"\n", "    if not LASPY_AVAILABLE:\n", "        return {'error': 'laspy not available', 'status': 'error'}\n", "\n", "    print(f\"\\nAnalyzing: {os.path.basename(las_file_path)}\")\n", "\n", "    results = {\n", "        'file_path': las_file_path,\n", "        'file_name': os.path.basename(las_file_path)\n", "    }\n", "\n", "    # Run all checks\n", "    print(\"  - Checking point density...\")\n", "    results['density'] = analyze_point_density(las_file_path)\n", "\n", "    print(\"  - Validating coordinate system...\")\n", "    results['coordinate_system'] = validate_coordinate_system(las_file_path)\n", "\n", "    print(\"  - Checking spatial extent...\")\n", "    results['spatial_extent'] = check_spatial_extent(las_file_path, expected_bounds)\n", "\n", "    print(\"  - Analyzing orientation & ground level...\")\n", "    results['orientation'] = check_orientation_and_ground(las_file_path)\n", "\n", "    print(\"  - Checking file integrity...\")\n", "    results['integrity'] = check_file_integrity(las_file_path)\n", "\n", "    print(\"  - Checking attribute channels...\")\n", "    results['attributes'] = check_attribute_channels(las_file_path)\n", "\n", "    return results"]}, {"cell_type": "markdown", "metadata": {"id": "AwgdJnTtJCEP"}, "source": ["### Results Summary and Visualization Functions"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"id": "ug1v_rseJCEP"}, "outputs": [], "source": ["import pandas as pd\n", "\n", "def create_quality_summary(results_list):\n", "    \"\"\"\n", "    Create a summary DataFrame from quality check results.\n", "    Each row corresponds to one LAS file's quality check.\n", "    \"\"\"\n", "    summary_data = []\n", "\n", "    for result in results_list:\n", "        # Skip if there was an error processing the file\n", "        if not result or 'error' in result:\n", "            continue\n", "\n", "        file_name = result.get('file_name', 'Unknown')\n", "\n", "        # Access DensityReport attributes directly\n", "        density_info = result.get('density')\n", "        num_points = density_info.num_points if density_info else 'N/A'\n", "        density_pts_per_m2 = density_info.density_pts_per_m2 if density_info else 0\n", "        area_m2 = density_info.area_m2 if density_info else 0\n", "\n", "\n", "        attr_report = result.get('attributes')\n", "        attr_checks = attr_report.attribute_checks if attr_report else {}\n", "\n", "        # Access CoordinateSystemReport attributes directly\n", "        coord_info = result.get('coordinate_system')\n", "        coord_epsg = coord_info.epsg_code if coord_info else 'Unknown'\n", "\n", "        # Access OrientationReport attributes directly\n", "        orientation_info = result.get('orientation')\n", "        z_stats = orientation_info.z_statistics if orientation_info else {}\n", "        issues = len(orientation_info.potential_issues) if orientation_info and orientation_info.potential_issues else 0\n", "\n", "        # Access LASIntegrityReport object and its attributes\n", "        integrity_info = result.get('integrity')\n", "        file_size_mb = integrity_info.integrity_checks.get('file_size_mb', 'N/A') if integrity_info and hasattr(integrity_info, 'integrity_checks') else 'N/A'\n", "\n", "\n", "        row = {\n", "            'File': file_name,\n", "            'Points': num_points,\n", "            'Density (pts/m²)': round(density_pts_per_m2, 2),\n", "            'Area (m²)': round(area_m2, 2),\n", "            'File Size (MB)': file_size_mb,\n", "            'Has RGB': attr_checks.get('has_rgb', False),\n", "            'Has Intensity': attr_checks.get('has_intensity', False),\n", "            'EPSG Code': coord_epsg,\n", "            'Z Range (m)': round(z_stats.get('z_range', 0), 2),\n", "            'Issues': issues\n", "        }\n", "\n", "        # Check integrity status and add error information if not success\n", "        if integrity_info and integrity_info.status != 'success':\n", "            row['Integrity Status'] = integrity_info.status\n", "            row['Integrity Error'] = integrity_info.error\n", "\n", "        summary_data.append(row)\n", "\n", "    return pd.DataFrame(summary_data)"]}, {"cell_type": "markdown", "metadata": {"id": "Kj1xFOShJCEP"}, "source": ["## Quality Assessment Execution\n", "\n", "This section executes the quality assessment and provides analysis of the results.\n", "\n", "**Instructions:**\n", "1. Update the `point_cloud_directory` path below\n", "2. Optionally set `expected_bounds` for your project area\n", "3. Run the cells to perform comprehensive quality assessment"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "HnuLKOiMJv97", "outputId": "6dc599c6-fd74-4af3-ae65-c5d92a597c9d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mounted at /content/drive\n"]}], "source": ["from google.colab import drive\n", "drive.mount(\"/content/drive\", force_remount=True)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MkMQoZfYJCEP", "outputId": "95d10fd1-9cc0-40fa-d581-cc65a986abc0"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 4 LAS/LAZ files\n", "  - /content/drive/MyDrive/thesis_datasets_20250613/thesis_datasets/pointcloud/rpcs/Point_Cloud.las\n", "  - /content/drive/MyDrive/thesis_datasets_20250613/thesis_datasets/pointcloud/rpcs/Point_Cloud_0.5.las\n", "  - /content/drive/MyDrive/thesis_datasets_20250613/thesis_datasets/pointcloud/mccarthy/Buffer_las(rev1).las\n", "  - /content/drive/MyDrive/thesis_datasets_20250613/thesis_datasets/pointcloud/castro/area4_point.las\n"]}], "source": ["# Configuration\n", "base_path = '/content/drive/MyDrive/thesis_datasets_20250613/thesis_datasets'\n", "point_cloud_directory = f'{base_path}/pointcloud'\n", "expected_bounds = None  # Optional: {'x_min': 0, 'x_max': 1000, 'y_min': 0, 'y_max': 1000}\n", "\n", "# Find all LAS/LAZ files\n", "las_files = []\n", "if os.path.exists(point_cloud_directory):\n", "    for ext in ['*.las', '*.laz', '*.LAS', '*.LAZ']:\n", "        las_files.extend(Path(point_cloud_directory).rglob(ext))\n", "\n", "    print(f\"Found {len(las_files)} LAS/LAZ files\")\n", "    for f in las_files:\n", "        print(f\"  - {f}\")\n", "else:\n", "    print(f\"Directory not found: {point_cloud_directory}\")\n", "    print(\"Please update the point_cloud_directory path above\")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 853}, "id": "FLo9jEcBJCEP", "outputId": "4d85c848-423e-44de-9a89-ec1ca0a7965d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Analyzing: Point_Cloud.las\n", "  - Checking point density...\n", "  - Validating coordinate system...\n", "  - Checking spatial extent...\n", "  - Analyzing orientation & ground level...\n", "  - Checking file integrity...\n", "  - Checking attribute channels...\n", "\n", "Analyzing: Point_Cloud_0.5.las\n", "  - Checking point density...\n", "  - Validating coordinate system...\n", "  - Checking spatial extent...\n", "  - Analyzing orientation & ground level...\n", "  - Checking file integrity...\n", "  - Checking attribute channels...\n", "\n", "Analyzing: Buffer_las(rev1).las\n", "  - Checking point density...\n", "  - Validating coordinate system...\n", "  - Checking spatial extent...\n", "  - Analyzing orientation & ground level...\n", "  - Checking file integrity...\n", "  - Checking attribute channels...\n", "\n", "Analyzing: area4_point.las\n", "  - Checking point density...\n", "  - Validating coordinate system...\n", "  - Checking spatial extent...\n", "  - Analyzing orientation & ground level...\n", "  - Checking file integrity...\n", "  - Checking attribute channels...\n", "\n", "================================================================================\n", "QUALITY ASSESSMENT SUMMARY\n", "================================================================================\n"]}, {"data": {"text/html": ["\n", "  <div id=\"df-7cd5ecaa-e789-413d-85ce-9f7a3e977739\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>File</th>\n", "      <th>Points</th>\n", "      <th>Density (pts/m²)</th>\n", "      <th>Area (m²)</th>\n", "      <th>File Size (MB)</th>\n", "      <th>Has RGB</th>\n", "      <th>Has Intensity</th>\n", "      <th>EPSG Code</th>\n", "      <th>Z Range (m)</th>\n", "      <th>Issues</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Point_Cloud.las</td>\n", "      <td>52862386</td>\n", "      <td>663.12</td>\n", "      <td>79717.57</td>\n", "      <td>1310.75</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>None</td>\n", "      <td>20.55</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Point_Cloud_0.5.las</td>\n", "      <td>4578587</td>\n", "      <td>58.68</td>\n", "      <td>78030.11</td>\n", "      <td>113.53</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>None</td>\n", "      <td>10.62</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Buffer_las(rev1).las</td>\n", "      <td>1457284</td>\n", "      <td>44.43</td>\n", "      <td>32796.13</td>\n", "      <td>36.13</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>None</td>\n", "      <td>4.50</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>area4_point.las</td>\n", "      <td>251056301</td>\n", "      <td>5426.65</td>\n", "      <td>46263.60</td>\n", "      <td>7182.78</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>None</td>\n", "      <td>17.42</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "      \n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-7cd5ecaa-e789-413d-85ce-9f7a3e977739')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "      \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "    \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-7cd5ecaa-e789-413d-85ce-9f7a3e977739 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-7cd5ecaa-e789-413d-85ce-9f7a3e977739');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "  \n", "    </div>\n", "  </div>\n", "  "], "text/plain": ["                   File     Points  Density (pts/m²)  Area (m²)  \\\n", "0       Point_Cloud.las   52862386            663.12   79717.57   \n", "1   Point_Cloud_0.5.las    4578587             58.68   78030.11   \n", "2  Buffer_las(rev1).las    1457284             44.43   32796.13   \n", "3       area4_point.las  251056301           5426.65   46263.60   \n", "\n", "   File Size (MB)  Has RGB  Has Intensity EPSG Code  Z Range (m)  Issues  \n", "0         1310.75     True           True      None        20.55       0  \n", "1          113.53     True           True      None        10.62       0  \n", "2           36.13     True           True      None         4.50       0  \n", "3         7182.78     True           True      None        17.42       0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Results saved to: point_cloud_quality_summary.csv\n"]}], "source": ["# Run comprehensive quality assessment\n", "if LASPY_AVAILABLE and las_files:\n", "    all_results = []\n", "\n", "    for las_file in las_files:\n", "        try:\n", "            result = comprehensive_quality_check(str(las_file), expected_bounds)\n", "            all_results.append(result)\n", "        except Exception as e:\n", "            print(f\"Error processing {las_file}: {e}\")\n", "\n", "    # Create summary\n", "    if all_results:\n", "        summary_df = create_quality_summary(all_results)\n", "        print(\"\\n\" + \"=\"*80)\n", "        print(\"QUALITY ASSESSMENT SUMMARY\")\n", "        print(\"=\"*80)\n", "        display(summary_df)\n", "\n", "        # Save results\n", "        summary_df.to_csv('point_cloud_quality_summary.csv', index=False)\n", "        print(\"\\nResults saved to: point_cloud_quality_summary.csv\")\n", "    else:\n", "        print(\"No results to display\")\n", "else:\n", "    print(\"Cannot run assessment: laspy not available or no LAS files found\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Quality Assessment Inference and Analysis\n", "\n", "This section provides detailed analysis and interpretation of the quality assessment results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_point_density_results(summary_df):\n", "    \"\"\"\n", "    Analyze point density patterns and provide insights.\n", "    \"\"\"\n", "    if summary_df.empty:\n", "        return \"No data available for analysis.\"\n", "    \n", "    density_col = 'Density (pts/m²)'\n", "    densities = summary_df[density_col]\n", "    \n", "    analysis = []\n", "    analysis.append(f\"Point Density Analysis:\")\n", "    analysis.append(f\"- Average density: {densities.mean():.2f} pts/m²\")\n", "    analysis.append(f\"- Density range: {densities.min():.2f} - {densities.max():.2f} pts/m²\")\n", "    analysis.append(f\"- Standard deviation: {densities.std():.2f} pts/m²\")\n", "    \n", "    # Density quality assessment\n", "    high_density = densities[densities > 500]\n", "    medium_density = densities[(densities >= 50) & (densities <= 500)]\n", "    low_density = densities[densities < 50]\n", "    \n", "    analysis.append(f\"\\nDensity Quality Distribution:\")\n", "    analysis.append(f\"- High density (>500 pts/m²): {len(high_density)} files\")\n", "    analysis.append(f\"- Medium density (50-500 pts/m²): {len(medium_density)} files\")\n", "    analysis.append(f\"- Low density (<50 pts/m²): {len(low_density)} files\")\n", "    \n", "    # Recommendations\n", "    analysis.append(f\"\\nRecommendations:\")\n", "    if len(high_density) > 0:\n", "        analysis.append(f\"- High-density files are excellent for detailed analysis and ML training\")\n", "    if len(low_density) > 0:\n", "        analysis.append(f\"- Low-density files may require preprocessing or may be unsuitable for fine-grained analysis\")\n", "    if densities.std() > 200:\n", "        analysis.append(f\"- High density variation suggests different acquisition methods or quality levels\")\n", "    \n", "    return \"\\n\".join(analysis)\n", "\n", "def analyze_file_characteristics(summary_df):\n", "    \"\"\"\n", "    Analyze file size and attribute characteristics.\n", "    \"\"\"\n", "    if summary_df.empty:\n", "        return \"No data available for analysis.\"\n", "    \n", "    analysis = []\n", "    analysis.append(f\"File Characteristics Analysis:\")\n", "    \n", "    # File size analysis\n", "    sizes = summary_df['File Size (MB)']\n", "    total_size = sizes.sum()\n", "    analysis.append(f\"- Total dataset size: {total_size:.2f} MB ({total_size/1024:.2f} GB)\")\n", "    analysis.append(f\"- Average file size: {sizes.mean():.2f} MB\")\n", "    analysis.append(f\"- Size range: {sizes.min():.2f} - {sizes.max():.2f} MB\")\n", "    \n", "    # Attribute availability\n", "    rgb_count = summary_df['Has RGB'].sum()\n", "    intensity_count = summary_df['Has Intensity'].sum()\n", "    total_files = len(summary_df)\n", "    \n", "    analysis.append(f\"\\nAttribute Availability:\")\n", "    analysis.append(f\"- RGB data: {rgb_count}/{total_files} files ({rgb_count/total_files*100:.1f}%)\")\n", "    analysis.append(f\"- Intensity data: {intensity_count}/{total_files} files ({intensity_count/total_files*100:.1f}%)\")\n", "    \n", "    # Coordinate system analysis\n", "    epsg_codes = summary_df['EPSG Code'].value_counts()\n", "    analysis.append(f\"\\nCoordinate System Status:\")\n", "    for epsg, count in epsg_codes.items():\n", "        analysis.append(f\"- {epsg}: {count} files\")\n", "    \n", "    return \"\\n\".join(analysis)\n", "\n", "def analyze_data_quality_issues(summary_df):\n", "    \"\"\"\n", "    Identify potential data quality issues and provide recommendations.\n", "    \"\"\"\n", "    if summary_df.empty:\n", "        return \"No data available for analysis.\"\n", "    \n", "    analysis = []\n", "    analysis.append(f\"Data Quality Issues and Recommendations:\")\n", "    \n", "    # Check for missing EPSG codes\n", "    missing_epsg = summary_df['EPSG Code'].isna().sum()\n", "    if missing_epsg > 0:\n", "        analysis.append(f\"\\nCoordinate System Issues:\")\n", "        analysis.append(f\"- {missing_epsg} files missing EPSG codes\")\n", "        analysis.append(f\"- Recommendation: Verify and assign proper coordinate reference systems\")\n", "    \n", "    # Check for elevation range issues\n", "    z_ranges = summary_df['Z Range (m)']\n", "    large_z_range = z_ranges[z_ranges > 50]\n", "    if len(large_z_range) > 0:\n", "        analysis.append(f\"\\nElevation Range Issues:\")\n", "        analysis.append(f\"- {len(large_z_range)} files with large elevation ranges (>50m)\")\n", "        analysis.append(f\"- May indicate outliers or mixed terrain types\")\n", "        analysis.append(f\"- Recommendation: Consider outlier removal or terrain classification\")\n", "    \n", "    # Check for potential issues\n", "    issues = summary_df['Issues'].sum()\n", "    if issues > 0:\n", "        analysis.append(f\"\\nDetected Issues:\")\n", "        analysis.append(f\"- Total issues detected: {issues}\")\n", "        analysis.append(f\"- Recommendation: Review individual file reports for specific issues\")\n", "    \n", "    # Overall assessment\n", "    analysis.append(f\"\\nOverall Data Quality Assessment:\")\n", "    if missing_epsg == 0 and len(large_z_range) == 0 and issues == 0:\n", "        analysis.append(f\"- Excellent: No major quality issues detected\")\n", "        analysis.append(f\"- All files appear suitable for analysis and ML training\")\n", "    elif missing_epsg + len(large_z_range) + issues <= 2:\n", "        analysis.append(f\"- Good: Minor issues detected, easily addressable\")\n", "        analysis.append(f\"- Files suitable for analysis with minimal preprocessing\")\n", "    else:\n", "        analysis.append(f\"- Moderate: Several issues detected\")\n", "        analysis.append(f\"- Recommend preprocessing and validation before analysis\")\n", "    \n", "    return \"\\n\".join(analysis)\n", "\n", "# Run inference analysis if results are available\n", "if 'summary_df' in locals() and not summary_df.empty:\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"DETAILED QUALITY ANALYSIS AND INFERENCE\")\n", "    print(\"=\"*80)\n", "    \n", "    print(\"\\n\" + analyze_point_density_results(summary_df))\n", "    print(\"\\n\" + \"-\"*60)\n", "    print(analyze_file_characteristics(summary_df))\n", "    print(\"\\n\" + \"-\"*60)\n", "    print(analyze_data_quality_issues(summary_df))\n", "else:\n", "    print(\"No summary data available for inference analysis.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# # Point Cloud Quality Assessment and Recommendations\n", "# \n", "---\n", "**Recommendation Criteria (Summary)**\n", "\n", "- Files are prioritized based on **point density** and **file size**.\n", "- EPSG code presence is checked for **coordinate system corrections**.\n", "- **High density variation** triggers normalization advice.\n", "- **Elevation range** is used to suggest outlier removal.\n", "- RGB and intensity availability guide **ML readiness** suggestions.\n", "\n", "**1. FILE PRIORITIZATION FOR ANALYSIS**\n", "\n", "**Primary Files**  \n", "(High-quality — recommended for initial analysis):\n", "- Files with **high point density** and **larger file sizes**\n", "- Typically well-suited for accurate analysis, training data preparation, and feature extraction\n", "\n", "**Secondary Files**  \n", "(Lower density — use for validation, testing, or backup):\n", "- Files with **lower point density** or **smaller size**\n", "- Useful for sanity checks, augmentation, or validation pipelines\n", "\n", "---\n", "\n", "**2. PREPROCESSING REQUIREMENTS**\n", "\n", "**Coordinate System Correction**\n", "- **Issue**: Some files lack EPSG code or proper spatial reference\n", "- **Action**: Assign or correct CRS using tools like GDAL, PDAL, or lasinfo\n", "- **Example Command**:\n", "  ```bash\n", "  gdal_translate -a_srs EPSG:XXXX input.las output.las\n", "---\n", "\n", "**3. DENSITY NORMALIZATION**\n", "\n", "- **Observation**: Variation in point densities across files\n", "- **Action**: Normalize density for consistent spatial analysis\n", "---\n", "\n", "**4.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ANALYSIS WORKFLOW**\n", "Machine Learning Preparation\n", "- **Check**: Ensure files contain relevant attributes such as RGB, intensity, and classification\n", "- **Action**: Use these features in downstream ML workflows (e.g., PointNet, DGCNN, Random Forest)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 0}