!pip install pandas numpy

import os
from pathlib import Path
import importlib

import pandas as pd
import numpy as np

# Detect whether running in Colab or Local
def is_colab():
    return importlib.util.find_spec("google.colab") is not None

IN_COLAB = is_colab()

if IN_COLAB:
    from google.colab import drive
    drive.mount("/content/drive", force_remount=True)
    base_path = Path("/content/drive/MyDrive/thesis_datasets_20250613/asbuilt-foundation-analysis/data/raw")
    output_path = Path("/content/drive/MyDrive/Colab Notebooks/asbuilt-foundation-analysis/data/analysis_output")
else:
    base_path = Path("../../data/raw/")
    output_path = Path("../../data/analysis_output")

output_path.mkdir(parents=True, exist_ok=True)

def get_file_info(base_path):
    entries = []
    for site_dir in base_path.iterdir():
        if not site_dir.is_dir():
            continue
        site_name = site_dir.name
        for subfolder in ['cad', 'pointcloud', 'ortho']:
            subdir = site_dir / subfolder
            if not subdir.exists():
                continue
            for root, _, files in os.walk(subdir):
                for file in files:
                    full_path = os.path.join(root, file)
                    size_mb = round(os.path.getsize(full_path) / (1024 * 1024), 2)
                    extension = os.path.splitext(file)[1].lower()
                    entries.append({
                        "Site": site_name,
                        "Data_Type": subfolder,
                        "File Path": full_path,
                        "Size (MB)": size_mb,
                        "Extension": extension
                    })
    return pd.DataFrame(entries)

def check_extension_availability(df, extension=".las"):
    df = df.copy()
    df['Extension'] = df['Extension'].str.lower()
    projects_with_ext = df[df['Extension'] == extension]['Site'].unique()

    return (
        df[['Site']].drop_duplicates().assign(
            **{f"has_{extension}": lambda x: x['Site'].isin(projects_with_ext).map({True: 'Yes', False: 'No'})}
        ).sort_values("Site").reset_index(drop=True)
    )

pd.set_option('display.max_rows', None)
pd.set_option('display.max_colwidth', None)  # Optional: shows full file paths

# Load All Files and Split by Type
all_files_df = get_file_info(base_path)

# Summary information
print("=" * 60)
print(f"Total File Entries: {len(all_files_df)}")
print(f"Unique Sites: {all_files_df['Site'].nunique()}")
print(f"Data Types Present: {all_files_df['Data_Type'].unique().tolist()}")
print(f"File Extensions: {sorted(all_files_df['Extension'].unique())}")
print("=" * 60)

all_files_df.to_csv(output_path / "all_file_info.csv", index=False)

pc_df = all_files_df[all_files_df['Data_Type'] == 'pointcloud']
check_extension_availability(pc_df, ".las")

pc_df

cad_df = all_files_df[all_files_df['Data_Type'] == 'cad']
check_extension_availability(cad_df, extension=".dwg")

cad_df

ortho_df = all_files_df[all_files_df['Data_Type'] == 'ortho']
check_extension_availability(ortho_df, ".tif")

ortho_df

def summarize_data(df, prefix):
    summary = df.groupby('Site').agg({
        'Size (MB)': ['sum', 'count'],
        'Extension': lambda x: ', '.join(sorted(x.unique()))
    }).round(2)
    summary.columns = [f"{prefix}_Size_MB", f"{prefix}_File_Count", f"{prefix}_Extensions"]
    return summary


pc_summary = summarize_data(pc_df, "PC")
cad_summary = summarize_data(cad_df, "CAD")
ortho_summary = summarize_data(ortho_df, "Ortho")

combined_summary = pd.concat([pc_summary, cad_summary, ortho_summary], axis=1).fillna({
    'PC_Size_MB': 0, 'PC_File_Count': 0, 'PC_Extensions': 'N/A',
    'CAD_Size_MB': 0, 'CAD_File_Count': 0, 'CAD_Extensions': 'N/A',
    'Ortho_Size_MB': 0, 'Ortho_File_Count': 0, 'Ortho_Extensions': 'N/A',
})
combined_summary[['PC_File_Count', 'CAD_File_Count', 'Ortho_File_Count']] = combined_summary[
    ['PC_File_Count', 'CAD_File_Count', 'Ortho_File_Count']].astype(int)

combined_summary['Total_Size_GB'] = (
    (combined_summary['PC_Size_MB'] + combined_summary['CAD_Size_MB'] + combined_summary['Ortho_Size_MB']) / 1024
).round(2)

combined_summary['Data_Completeness'] = (
    (combined_summary['PC_File_Count'] > 0).astype(int) +
    (combined_summary['CAD_File_Count'] > 0).astype(int) +
    (combined_summary['Ortho_File_Count'] > 0).astype(int)
)

combined_summary.to_csv(output_path / "site_data_summary.csv")
combined_summary
