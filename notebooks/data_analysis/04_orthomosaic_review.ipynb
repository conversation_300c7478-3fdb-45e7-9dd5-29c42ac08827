!python -m pip install rasterio

# Import required libraries
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
import rasterio

# Import additional libraries
import pandas as pd
from tqdm import tqdm

print("Orthomosaic GeoTIFF Review - Starting...")
print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Setup paths
project_root = Path('../..')  # Navigate to project root from notebooks/data_analysis/
data_path = project_root / 'data'
raw_path = data_path / 'raw'
output_path = data_path / 'output_runs'

# Create output directory
output_path.mkdir(parents=True, exist_ok=True)

print(f"Project root: {project_root.resolve()}")
print(f"Raw data path: {raw_path.resolve()}")
print(f"Output path: {output_path.resolve()}")

# Discover orthomosaic GeoTIFF files
def discover_orthomosaic_files(base_path: Path) -> List[Path]:
    """Discover GeoTIFF files in the data directory."""
    extensions = ['*.tif', '*.tiff', '*.TIF', '*.TIFF']
    files = []
    
    for ext in extensions:
        files.extend(base_path.rglob(ext))
    
    return sorted(files)

# Find orthomosaic files
ortho_files = discover_orthomosaic_files(raw_path)

print(f"\nDISCOVERED ORTHOMOSAIC FILES:")
print("=" * 50)
print(f"Total GeoTIFF files found: {len(ortho_files)}")

if ortho_files:
    for i, file_path in enumerate(ortho_files, 1):
        relative_path = file_path.relative_to(raw_path)
        file_size_mb = file_path.stat().st_size / (1024 * 1024)
        print(f"  {i:2d}. {relative_path} ({file_size_mb:.1f} MB)")
else:
    print("  No GeoTIFF files found!")
    print(f"  Search path: {raw_path}")
    print(f"  Extensions: .tif, .tiff")

# Simple orthomosaic assessment function
def assess_orthomosaic_simple(file_path: Path) -> Dict[str, Any]:
    """Perform basic assessment on an orthomosaic GeoTIFF file."""
    
    try:
        with rasterio.open(file_path) as src:
            width, height = src.width, src.height
            bands = src.count
            dtype = str(src.dtypes[0])
            crs = src.crs.to_string() if src.crs else "Unknown"

            transform = src.transform
            pixel_size_x = abs(transform[0])
            pixel_size_y = abs(transform[4])

            bounds = src.bounds
            area_m2 = (bounds.right - bounds.left) * (bounds.top - bounds.bottom)
            area_km2 = area_m2 / 1_000_000

            resolution_category = (
                "High" if pixel_size_x < 0.1 else
                "Medium" if pixel_size_x < 0.5 else
                "Low"
            )

            return {
                "file_path": str(file_path),
                "file_size_mb": file_path.stat().st_size / (1024 * 1024),
                "dimensions": {"width": width, "height": height},
                "bands": bands,
                "data_type": dtype,
                "crs": crs,
                "pixel_size_m": {"x": pixel_size_x, "y": pixel_size_y},
                "bounds": {
                    "left": bounds.left, "bottom": bounds.bottom,
                    "right": bounds.right, "top": bounds.top
                },
                "coverage_area_km2": area_km2,
                "resolution_category": resolution_category,
                "suitable_for_overlay": pixel_size_x < 1.0,
                "suitable_for_visualization": True,
                "suitable_for_2d_baseline": pixel_size_x < 0.5
            }

    except Exception as e:
        return {
            "file_path": str(file_path),
            "error": str(e),
            "suitable_for_overlay": False,
            "suitable_for_visualization": False,
            "suitable_for_2d_baseline": False
        }

print("Orthomosaic assessment function defined")

# Assess all orthomosaic files
ortho_results = []

if ortho_files:
    print("\nASSESSING ORTHOMOSAIC FILES:")
    print("=" * 50)
    
    for file_path in tqdm(ortho_files, desc="Processing orthomosaics"):
        result = assess_orthomosaic_simple(file_path)
        result["file"] = str(file_path.relative_to(raw_path))  # relative filename for table
        ortho_results.append(result)

    # Build DataFrame
    records = []
    for res in ortho_results:
        if 'error' in res:
            records.append({
                "File": res["file"],
                "Size (MB)": None,
                "Dimensions": "ERROR",
                "Bands": None,
                "Resolution (m/pixel)": None,
                "Resolution Category": None,
                "Coverage (km²)": None,
                "CRS": None,
                "Overlay": "N/A",
                "Visualization": "N/A",
                "2D Baseline": "N/A",
                "Note/Error": res.get("error")
            })
        else:
            records.append({
                "File": res["file"],
                "Size (MB)": round(res["file_size_mb"], 1),
                "Dimensions": f"{res['dimensions']['width']} x {res['dimensions']['height']}",
                "Bands": res["bands"],
                "Resolution (m/pixel)": round(res["pixel_size_m"]["x"], 3),
                "Resolution Category": res["resolution_category"],
                "Coverage (km²)": round(res["coverage_area_km2"], 2),
                "CRS": res["crs"],
                "Overlay": "YES" if res["suitable_for_overlay"] else "NO",
                "Visualization": "YES" if res["suitable_for_visualization"] else "NO",
                "2D Baseline": "YES" if res["suitable_for_2d_baseline"] else "NO",
                "Note/Error": res.get("note", "")
            })

    df_results = pd.DataFrame(records)
    display(df_results)  # Works in notebooks

    # Optional: Save to CSV
    df_results.to_csv(f"{output_path}/orthomosaic_quality_assessment.csv", index=False)
else:
    print("\nNo orthomosaic files found for assessment.")


if ortho_results:
    overlay_count = sum(r.get('suitable_for_overlay', False) for r in ortho_results)
    viz_count = sum(r.get('suitable_for_visualization', False) for r in ortho_results)
    baseline_count = sum(r.get('suitable_for_2d_baseline', False) for r in ortho_results)

    print("ORTHOMOSAIC SUMMARY:")
    print("=" * 50)
    print(f"Total files: {len(ortho_results)}")
    print(f"Suitable for overlay: {overlay_count}")
    print(f"Suitable for visualization: {viz_count}")
    print(f"Suitable for 2D baseline: {baseline_count}")

    # Save full results
    results_path = output_path / f"orthomosaic_assessment_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_path, 'w') as f:
        json.dump({
            'assessment_date': datetime.now().isoformat(),
            'total_files': len(ortho_results),
            'summary': {
                'overlay_suitable': overlay_count,
                'visualization_suitable': viz_count,
                'baseline_suitable': baseline_count
            },
            'files': ortho_results
        }, f, indent=2)

    print(f"\nResults saved: {results_path}")

else:
    print("No orthomosaic files available for assessment.")
