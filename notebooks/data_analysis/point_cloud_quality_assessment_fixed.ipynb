# Papermill Parameters - Override these when running with papermill
project_type = "foundation_analysis"  # Project type for data organization
site_name = "multi_site"              # Site identifier (use 'multi_site' for all sites)
specific_files = []                   # Optional: specific files to assess (empty = all files)

# Quality Assessment Parameters
min_point_density = 10.0             # Minimum acceptable points per m² 
max_point_density = 1000.0           # Maximum reasonable points per m²
elevation_outlier_threshold = 3.0    # Standard deviations for elevation outliers
coordinate_precision = 0.01          # Minimum coordinate precision (meters)

# Processing Parameters
sample_size = 100000                 # Sample size for large files (0 = use all points)
generate_visualizations = True       # Create quality assessment plots
save_detailed_reports = True         # Save detailed per-file reports
create_summary_report = True         # Create overall summary report
use_mlflow = True                    # Enable MLflow tracking

# Import required libraries
import os
import json
import logging
import warnings
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import laspy
from tqdm import tqdm

# MLflow for experiment tracking
if use_mlflow:
    import mlflow
    import mlflow.sklearn

# Configure settings
warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")

print("Point Cloud Quality Assessment - Starting...")
print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Setup paths following project data organization
project_root = Path('../..')  # Navigate to project root from notebooks/data_analysis/
data_path = project_root / 'data'
raw_path = data_path / 'raw'
output_path = data_path / 'output_runs'

# Create output directory
output_path.mkdir(parents=True, exist_ok=True)

print(f"Project root: {project_root.resolve()}")
print(f"Raw data path: {raw_path.resolve()}")
print(f"Output path: {output_path.resolve()}")

# Initialize MLflow if enabled
if use_mlflow:
    mlflow.set_experiment(f"point_cloud_quality_{project_type}")
    mlflow.start_run(run_name=f"quality_assessment_{site_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    mlflow.log_param("project_type", project_type)
    mlflow.log_param("site_name", site_name)
    mlflow.log_param("min_point_density", min_point_density)
    mlflow.log_param("max_point_density", max_point_density)
    mlflow.log_param("sample_size", sample_size)

# Discover point cloud files
def discover_point_cloud_files(base_path: Path, specific_files: List[str] = None) -> List[Path]:
    """Discover LAS/LAZ files in the data directory."""
    
    if specific_files:
        # Use specific files if provided
        files = []
        for file_name in specific_files:
            file_path = base_path / file_name
            if file_path.exists():
                files.append(file_path)
            else:
                logger.warning(f"Specific file not found: {file_path}")
        return files
    else:
        # Discover all LAS/LAZ files recursively
        extensions = ['*.las', '*.laz']
        files = []
        
        for ext in extensions:
            files.extend(base_path.rglob(ext))
        
        return sorted(files)

# Discover files
point_cloud_files = discover_point_cloud_files(raw_path, specific_files)

print(f"\nDISCOVERED POINT CLOUD FILES:")
print("=" * 50)
print(f"Total files found: {len(point_cloud_files)}")

if point_cloud_files:
    for i, file_path in enumerate(point_cloud_files, 1):
        relative_path = file_path.relative_to(raw_path)
        file_size_mb = file_path.stat().st_size / (1024 * 1024)
        print(f"  {i:2d}. {relative_path} ({file_size_mb:.1f} MB)")
else:
    print("  No point cloud files found!")
    print(f"  Search path: {raw_path}")
    print(f"  Extensions: .las, .laz")

# Log to MLflow
if use_mlflow:
    mlflow.log_metric("files_discovered", len(point_cloud_files))
    total_size_mb = sum(f.stat().st_size for f in point_cloud_files) / (1024 * 1024)
    mlflow.log_metric("total_size_mb", total_size_mb)

def assess_point_cloud_quality(file_path: Path, sample_size: int = 0) -> Dict[str, Any]:
    """Perform comprehensive quality assessment on a point cloud file."""
    
    try:
        # Load LAS file
        las_file = laspy.read(file_path)
        
        # Basic file information
        total_points = len(las_file.points)
        
        # Sample points if file is large
        if sample_size > 0 and total_points > sample_size:
            indices = np.random.choice(total_points, sample_size, replace=False)
            x, y, z = las_file.x[indices], las_file.y[indices], las_file.z[indices]
            sampled = True
        else:
            x, y, z = las_file.x, las_file.y, las_file.z
            sampled = False
        
        # Spatial extent
        bounds = {
            'x_min': float(np.min(x)), 'x_max': float(np.max(x)),
            'y_min': float(np.min(y)), 'y_max': float(np.max(y)),
            'z_min': float(np.min(z)), 'z_max': float(np.max(z))
        }
        
        # Calculate area and point density
        area_m2 = (bounds['x_max'] - bounds['x_min']) * (bounds['y_max'] - bounds['y_min'])
        point_density = total_points / area_m2 if area_m2 > 0 else 0
        
        # Elevation statistics
        z_stats = {
            'mean': float(np.mean(z)),
            'std': float(np.std(z)),
            'min': float(np.min(z)),
            'max': float(np.max(z)),
            'range': float(np.max(z) - np.min(z))
        }
        
        # Detect elevation outliers
        z_outliers = np.abs(z - z_stats['mean']) > (elevation_outlier_threshold * z_stats['std'])
        outlier_ratio = np.sum(z_outliers) / len(z)
        
        # Check available attributes
        attributes = {
            'has_intensity': hasattr(las_file, 'intensity'),
            'has_rgb': hasattr(las_file, 'red') and hasattr(las_file, 'green') and hasattr(las_file, 'blue'),
            'has_classification': hasattr(las_file, 'classification'),
            'has_return_info': hasattr(las_file, 'return_number')
        }
        
        # Coordinate precision check
        coord_precision = {
            'x_precision': float(np.min(np.diff(np.sort(np.unique(x[:1000]))))),
            'y_precision': float(np.min(np.diff(np.sort(np.unique(y[:1000]))))),
            'z_precision': float(np.min(np.diff(np.sort(np.unique(z[:1000])))))
        }
        
        # Quality flags
        quality_flags = {
            'density_ok': min_point_density <= point_density <= max_point_density,
            'precision_ok': all(p >= coordinate_precision for p in coord_precision.values()),
            'outliers_ok': outlier_ratio < 0.05,  # Less than 5% outliers
            'bounds_ok': area_m2 > 0 and z_stats['range'] > 0
        }
        
        return {
            'file_path': str(file_path),
            'file_size_mb': file_path.stat().st_size / (1024 * 1024),
            'total_points': total_points,
            'sampled_points': len(x),
            'was_sampled': sampled,
            'bounds': bounds,
            'area_m2': area_m2,
            'point_density': point_density,
            'elevation_stats': z_stats,
            'outlier_ratio': outlier_ratio,
            'attributes': attributes,
            'coordinate_precision': coord_precision,
            'quality_flags': quality_flags,
            'overall_quality': all(quality_flags.values())
        }
        
    except Exception as e:
        logger.error(f"Error assessing {file_path}: {str(e)}")
        return {
            'file_path': str(file_path),
            'error': str(e),
            'overall_quality': False
        }

print("Quality assessment functions defined")