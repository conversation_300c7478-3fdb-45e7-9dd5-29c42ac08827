{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Point Cloud Data Quality Assessment\n", "\n", "**Stage**: Data Analysis - Quality Assessment  \n", "**Input Data**: Point cloud files (.las, .laz) from data/raw directory  \n", "**Output**: Quality assessment reports and metrics saved to data/output_runs  \n", "**Format**: JSON reports, CSV metrics, and visualization plots  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis\n", "\n", "## Overview\n", "\n", "Performs comprehensive quality checks on point cloud data to ensure data integrity and suitability for foundation analysis:\n", "\n", "- **Point Density Analysis** - Points per square meter assessment\n", "- **Coordinate System Validation** - EPSG code and CRS verification\n", "- **Spatial Extent Consistency** - Bounding box validation\n", "- **Elevation Analysis** - Ground level and outlier detection\n", "- **File Integrity Checks** - Corruption and format validation\n", "- **Attribute Channel Analysis** - Intensity, RGB, and classification data\n", "\n", "## Workflow Steps\n", "\n", "1. **Setup & Configuration** - Parameters and paths\n", "2. **Data Discovery** - Find and inventory point cloud files\n", "3. **Quality Assessment** - Perform comprehensive quality checks\n", "4. **Analysis & Reporting** - Generate metrics and visualizations\n", "5. **Output Generation** - Save assessment results and recommendations"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill Parameters - Override these when running with papermill\n", "project_type = \"foundation_analysis\"  # Project type for data organization\n", "site_name = \"multi_site\"              # Site identifier (use 'multi_site' for all sites)\n", "specific_files = []                   # Optional: specific files to assess (empty = all files)\n", "\n", "# Quality Assessment Parameters\n", "min_point_density = 10.0             # Minimum acceptable points per m² \n", "max_point_density = 1000.0           # Maximum reasonable points per m²\n", "elevation_outlier_threshold = 3.0    # Standard deviations for elevation outliers\n", "coordinate_precision = 0.01          # Minimum coordinate precision (meters)\n", "\n", "# Processing Parameters\n", "sample_size = 100000                 # Sample size for large files (0 = use all points)\n", "generate_visualizations = True       # Create quality assessment plots\n", "save_detailed_reports = True         # Save detailed per-file reports\n", "create_summary_report = True         # Create overall summary report\n", "use_mlflow = True                    # Enable MLflow tracking"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import os\n", "import json\n", "import logging\n", "import warnings\n", "from pathlib import Path\n", "from datetime import datetime\n", "from typing import Dict, List, Any, Optional\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import laspy\n", "from tqdm import tqdm\n", "\n", "# MLflow for experiment tracking\n", "if use_mlflow:\n", "    import mlflow\n", "    import mlflow.sklearn\n", "\n", "# Configure settings\n", "warnings.filterwarnings('ignore')\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Point Cloud Quality Assessment - Starting...\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup paths following project data organization\n", "project_root = Path('../..')  # Navigate to project root from notebooks/data_analysis/\n", "data_path = project_root / 'data'\n", "raw_path = data_path / 'raw'\n", "output_path = data_path / 'output_runs'\n", "\n", "# Create output directory\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"Project root: {project_root.resolve()}\")\n", "print(f\"Raw data path: {raw_path.resolve()}\")\n", "print(f\"Output path: {output_path.resolve()}\")\n", "\n", "# Initialize MLflow if enabled\n", "if use_mlflow:\n", "    mlflow.set_experiment(f\"point_cloud_quality_{project_type}\")\n", "    mlflow.start_run(run_name=f\"quality_assessment_{site_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}\")\n", "    mlflow.log_param(\"project_type\", project_type)\n", "    mlflow.log_param(\"site_name\", site_name)\n", "    mlflow.log_param(\"min_point_density\", min_point_density)\n", "    mlflow.log_param(\"max_point_density\", max_point_density)\n", "    mlflow.log_param(\"sample_size\", sample_size)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Point Cloud Discovery\n", "\n", "Find and inventory all point cloud files:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Discover point cloud files\n", "def discover_point_cloud_files(base_path: Path, specific_files: List[str] = None) -> List[Path]:\n", "    \"\"\"Discover LAS/LAZ files in the data directory.\"\"\"\n", "    \n", "    if specific_files:\n", "        # Use specific files if provided\n", "        files = []\n", "        for file_name in specific_files:\n", "            file_path = base_path / file_name\n", "            if file_path.exists():\n", "                files.append(file_path)\n", "            else:\n", "                logger.warning(f\"Specific file not found: {file_path}\")\n", "        return files\n", "    else:\n", "        # Discover all LAS/LAZ files recursively\n", "        extensions = ['*.las', '*.laz']\n", "        files = []\n", "        \n", "        for ext in extensions:\n", "            files.extend(base_path.rglob(ext))\n", "        \n", "        return sorted(files)\n", "\n", "# Discover files\n", "point_cloud_files = discover_point_cloud_files(raw_path, specific_files)\n", "\n", "print(f\"\\nDISCOVERED POINT CLOUD FILES:\")\n", "print(\"=\" * 50)\n", "print(f\"Total files found: {len(point_cloud_files)}\")\n", "\n", "if point_cloud_files:\n", "    for i, file_path in enumerate(point_cloud_files, 1):\n", "        relative_path = file_path.relative_to(raw_path)\n", "        file_size_mb = file_path.stat().st_size / (1024 * 1024)\n", "        print(f\"  {i:2d}. {relative_path} ({file_size_mb:.1f} MB)\")\n", "else:\n", "    print(\"  No point cloud files found!\")\n", "    print(f\"  Search path: {raw_path}\")\n", "    print(f\"  Extensions: .las, .laz\")\n", "\n", "# Log to MLflow\n", "if use_mlflow:\n", "    mlflow.log_metric(\"files_discovered\", len(point_cloud_files))\n", "    total_size_mb = sum(f.stat().st_size for f in point_cloud_files) / (1024 * 1024)\n", "    mlflow.log_metric(\"total_size_mb\", total_size_mb)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Quality Assessment Functions\n", "\n", "Define functions for comprehensive quality checks:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def assess_point_cloud_quality(file_path: Path, sample_size: int = 0) -> Dict[str, Any]:\n", "    \"\"\"Perform comprehensive quality assessment on a point cloud file.\"\"\"\n", "    \n", "    try:\n", "        # Load LAS file\n", "        las_file = laspy.read(file_path)\n", "        \n", "        # Basic file information\n", "        total_points = len(las_file.points)\n", "        \n", "        # Sample points if file is large\n", "        if sample_size > 0 and total_points > sample_size:\n", "            indices = np.random.choice(total_points, sample_size, replace=False)\n", "            x, y, z = las_file.x[indices], las_file.y[indices], las_file.z[indices]\n", "            sampled = True\n", "        else:\n", "            x, y, z = las_file.x, las_file.y, las_file.z\n", "            sampled = False\n", "        \n", "        # Spatial extent\n", "        bounds = {\n", "            'x_min': float(np.min(x)), 'x_max': float(np.max(x)),\n", "            'y_min': float(np.min(y)), 'y_max': float(np.max(y)),\n", "            'z_min': float(np.min(z)), 'z_max': float(np.max(z))\n", "        }\n", "        \n", "        # Calculate area and point density\n", "        area_m2 = (bounds['x_max'] - bounds['x_min']) * (bounds['y_max'] - bounds['y_min'])\n", "        point_density = total_points / area_m2 if area_m2 > 0 else 0\n", "        \n", "        # Elevation statistics\n", "        z_stats = {\n", "            'mean': float(np.mean(z)),\n", "            'std': float(np.std(z)),\n", "            'min': float(np.min(z)),\n", "            'max': float(np.max(z)),\n", "            'range': float(np.max(z) - np.min(z))\n", "        }\n", "        \n", "        # Detect elevation outliers\n", "        z_outliers = np.abs(z - z_stats['mean']) > (elevation_outlier_threshold * z_stats['std'])\n", "        outlier_ratio = np.sum(z_outliers) / len(z)\n", "        \n", "        # Check available attributes\n", "        attributes = {\n", "            'has_intensity': hasattr(las_file, 'intensity'),\n", "            'has_rgb': hasattr(las_file, 'red') and hasattr(las_file, 'green') and hasattr(las_file, 'blue'),\n", "            'has_classification': hasattr(las_file, 'classification'),\n", "            'has_return_info': hasattr(las_file, 'return_number')\n", "        }\n", "        \n", "        # Coordinate precision check\n", "        coord_precision = {\n", "            'x_precision': float(np.min(np.diff(np.sort(np.unique(x[:1000]))))),\n", "            'y_precision': float(np.min(np.diff(np.sort(np.unique(y[:1000]))))),\n", "            'z_precision': float(np.min(np.diff(np.sort(np.unique(z[:1000])))))\n", "        }\n", "        \n", "        # Quality flags\n", "        quality_flags = {\n", "            'density_ok': min_point_density <= point_density <= max_point_density,\n", "            'precision_ok': all(p >= coordinate_precision for p in coord_precision.values()),\n", "            'outliers_ok': outlier_ratio < 0.05,  # Less than 5% outliers\n", "            'bounds_ok': area_m2 > 0 and z_stats['range'] > 0\n", "        }\n", "        \n", "        return {\n", "            'file_path': str(file_path),\n", "            'file_size_mb': file_path.stat().st_size / (1024 * 1024),\n", "            'total_points': total_points,\n", "            'sampled_points': len(x),\n", "            'was_sampled': sampled,\n", "            'bounds': bounds,\n", "            'area_m2': area_m2,\n", "            'point_density': point_density,\n", "            'elevation_stats': z_stats,\n", "            'outlier_ratio': outlier_ratio,\n", "            'attributes': attributes,\n", "            'coordinate_precision': coord_precision,\n", "            'quality_flags': quality_flags,\n", "            'overall_quality': all(quality_flags.values())\n", "        }\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error assessing {file_path}: {str(e)}\")\n", "        return {\n", "            'file_path': str(file_path),\n", "            'error': str(e),\n", "            'overall_quality': <PERSON>alse\n", "        }\n", "\n", "print(\"Quality assessment functions defined\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}