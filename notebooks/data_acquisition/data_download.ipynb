from google.colab import drive
drive.mount('/content/drive')

import subprocess
from pathlib import Path

# Setup base path
base_path = Path("../../data/raw")
base_path.mkdir(parents=True, exist_ok=True)

def run(cmd):
    cmd = cmd.strip()

    if cmd.startswith("aws s3 cp --recursive"):
        # Format: aws s3 cp --recursive <src> <dest>
        parts = cmd.split()
        if len(parts) < 5:
            raise ValueError("Invalid S3 command. Expecting: aws s3 cp --recursive <src> <dest>")
        dest_path = Path(parts[-1]).expanduser()
        dest_path.mkdir(parents=True, exist_ok=True)

        if any(dest_path.iterdir()):
            print(f"Skipping S3 download, folder already populated: {dest_path}")
            return

    elif cmd.startswith("wget"):
        # Format: wget <url> -O <dest>
        parts = cmd.split()
        if "-O" not in parts:
            raise ValueError("wget command must include '-O <dest_path>'")
        dest_index = parts.index("-O") + 1
        if dest_index >= len(parts):
            raise ValueError("Missing destination path after '-O'")
        dest_path = Path(parts[dest_index]).expanduser()
        dest_path.parent.mkdir(parents=True, exist_ok=True)

        if dest_path.exists():
            print(f"Skipping wget download, file already exists: {dest_path}")
            return

    else:
        raise ValueError("Only 'aws s3 cp --recursive' and 'wget -O <dest>' supported")

    # Run the command
    print(f"Downloading to: {dest_path}")
    subprocess.run(cmd, shell=True, check=True)


# 1. Motali De Castro - ENEL
proj = base_path / "motali_de_castro"
proj.mkdir(exist_ok=True)
run("aws s3 cp --recursive s3://preetam-filezilla-test/Castro/Pointcloud/ {}/pointcloud/".format(proj))
run("aws s3 cp --recursive s3://ftp-enel/montalto_di_castro-italy/2025-01-30/CAD/ {}/cad/".format(proj))
run("aws s3 cp --recursive s3://preetam-filezilla-test/Castro/Ortho/ {}/ortho/".format(proj))



# 2. Mudjar - ENEL
proj = base_path / "mudjar_enel"
proj.mkdir(exist_ok=True)
#run("wget -O {}/pointcloud.las 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las'".format(proj))
run("aws s3 cp --recursive s3://ftp-enel/mudejar-spain/ {}/cad/".format(proj))
#run("wget -O {}/ortho.tif 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las'".format(proj))

# 3. Piani Di Giorgio - ENEL
proj = base_path / "piani_di_giorgio"
proj.mkdir(exist_ok=True)
#run("wget -O {}/pointcloud.las 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Paini_Di_Di_Giorgio/Flight12/Giorgio_Fly12_pointcloud.las'".format(proj))
run("aws s3 cp --recursive s3://ftp-enel/pian_di_di_giorgio-italy/2024-05-15/ {}/cad/".format(proj))
#run("wget -O {}/ortho.tif 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Paini_Di_Di_Giorgio/Flight12/Giorgio_Fly12_Ortho.tif'".format(proj))

# 4. Sunstreams Project - McCarthy
proj = base_path / "sunstreams_mccarthy"
proj.mkdir(exist_ok=True)
run("aws s3 cp --recursive s3://preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/ {}/pointcloud/".format(proj))
run("aws s3 cp --recursive s3://ftp-mccarthy/CAD Files/ {}/cad/".format(proj))
run("aws s3 cp --recursive s3://preetam-filezilla-test/McCarthy_Fly2/RGB_Ortho/ {}/ortho/".format(proj))

# 5. Althea - RPCS
proj = base_path / "althea_rpcs"
proj.mkdir(exist_ok=True)
run("aws s3 cp --recursive s3://preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/ {}/pointcloud/".format(proj))
run("aws s3 cp --recursive s3://ftp-rpcs/Althea/CAD Files/ {}/cad/".format(proj))
run("aws s3 cp --recursive s3://preetam-filezilla-test/RCPS/Updated_031024/Ortho/ {}/ortho/".format(proj))

# 6. Nortan - RES Renewables
proj = base_path / "nortan_res"
proj.mkdir(exist_ok=True)
#run("aws s3 cp --recursive 's3://ftp-upload-images/Data files to GIS Team/CPM & CQM/2025/RES/Block11/Pointcloud/' {}/pointcloud/".format(proj))
run("aws s3 cp --recursive s3://preetam-filezilla-test/RES_Renewable/BLOCK_11/CAD/ {}/cad/".format(proj))
#run("wget -O {}/ortho.tif 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2025/RES/Block11/RSE_Block11_ortho.tif'".format(proj))

# Install rclone if not already available
!curl https://rclone.org/install.sh | sudo bash

%bash%
# Sync all project folders and subfolders to Drive
! rclone sync "../../data/raw/" "gdrive:asbuilt-foundation/" --progress

%bash%
# Sync everything back from Drive to local
!rclone sync "gdrive:asbuilt-foundation/" "../../data/raw/" --progress
