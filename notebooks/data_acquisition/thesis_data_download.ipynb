# %% [markdown]
# # Project Data Downloader (Bare Minimum)
# Downloads point cloud, CAD, and ortho data using AWS S3 CLI or `wget` for HTTP links.
# 
# **Author**: Preetam Balijepalli  
# **Date**: June 2025

# %%
import subprocess
from pathlib import Path

# Setup destination
base_path = Path("../../data/raw/foundation_analysis")
base_path.mkdir(parents=True, exist_ok=True)

# Simple function to run shell commands
def run(cmd):
    print(f"Running: {cmd}")
    subprocess.run(cmd, shell=True, check=False)

# %%
# 1. <PERSON><PERSON><PERSON> - <PERSON>NEL
proj = base_path / "motali_de_castro"
proj.mkdir(exist_ok=True)
run("aws s3 cp --recursive s3://preetam-filezilla-test/Castro/Pointcloud/ {}/pointcloud/".format(proj))
run("aws s3 cp --recursive s3://ftp-enel/montalto_di_castro-italy/2025-01-30/CAD/ {}/cad/".format(proj))
run("aws s3 cp --recursive s3://preetam-filezilla-test/Castro/Ortho/ {}/ortho/".format(proj))

# %%
# 2. Mudjar - ENEL
proj = base_path / "mudjar_enel"
proj.mkdir(exist_ok=True)
run("wget -O {}/pointcloud.las 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las'".format(proj))
run("aws s3 cp --recursive s3://ftp-enel/mudejar-spain/ {}/cad/".format(proj))
run("wget -O {}/ortho.tif 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las'".format(proj))

# %%
# 3. Piani Di Giorgio - ENEL
proj = base_path / "piani_di_giorgio"
proj.mkdir(exist_ok=True)
run("wget -O {}/pointcloud.las 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Paini_Di_Di_Giorgio/Flight12/Giorgio_Fly12_pointcloud.las'".format(proj))
run("aws s3 cp --recursive s3://ftp-enel/pian_di_di_giorgio-italy/2024-05-15/ {}/cad/".format(proj))
run("wget -O {}/ortho.tif 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Paini_Di_Di_Giorgio/Flight12/Giorgio_Fly12_Ortho.tif'".format(proj))

# %%
# 4. Sunstreams Project - McCarthy
proj = base_path / "sunstreams_mccarthy"
proj.mkdir(exist_ok=True)
run("aws s3 cp --recursive s3://preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/ {}/pointcloud/".format(proj))
run("aws s3 cp --recursive s3://ftp-mccarthy/CAD Files/ {}/cad/".format(proj))
run("aws s3 cp --recursive s3://preetam-filezilla-test/McCarthy_Fly2/RGB_Ortho/ {}/ortho/".format(proj))

# %%
# 5. Althea - RPCS
proj = base_path / "althea_rpcs"
proj.mkdir(exist_ok=True)
run("aws s3 cp --recursive s3://preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/ {}/pointcloud/".format(proj))
run("aws s3 cp --recursive s3://ftp-rpcs/Althea/CAD Files/ {}/cad/".format(proj))
run("aws s3 cp --recursive s3://preetam-filezilla-test/RCPS/Updated_031024/Ortho/ {}/ortho/".format(proj))

# %%
# 6. Nortan - RES Renewables
proj = base_path / "nortan_res"
proj.mkdir(exist_ok=True)
run("aws s3 cp --recursive 's3://ftp-upload-images/Data files to GIS Team/CPM & CQM/2025/RES/Block11/Pointcloud/' {}/pointcloud/".format(proj))
run("aws s3 cp --recursive s3://preetam-filezilla-test/RES_Renewable/BLOCK_11/CAD/ {}/cad/".format(proj))
run("wget -O {}/ortho.tif 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2025/RES/Block11/RSE_Block11_ortho.tif'".format(proj))
