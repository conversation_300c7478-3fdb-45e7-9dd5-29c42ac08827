{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Data Acquisition: Point Cloud Download\n", "\n", "**Stage**: Data Acquisition  \n", "**Input Data**: Remote S3 datasets  \n", "**Output**: Point cloud files in data/raw directory  \n", "**Format**: LAS files for point cloud processing  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis\n", "\n", "## Overview\n", "\n", "Downloads optimal datasets for foundation analysis research:\n", "- Castro Area4 (7 GB) - Primary dataset for method development\n", "- RPCS Point Cloud (1.3 GB) - Secondary dataset for validation\n", "- <PERSON> (36 MB) - Testing dataset for quick iterations\n", "\n", "**Total**: ~8.3 GB of point cloud data\n", "**Method**: Using rclone for reliable S3 downloads with progress tracking"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill Parameters - Override these when running with papermill\n", "project_type = \"foundation_analysis\"  # Project type for data organization\n", "site_name = \"multi_site\"              # Site identifier for this download session\n", "download_castro = True                # Download Castro Area4 dataset\n", "download_rpcs = True                  # Download RPCS Point Cloud dataset\n", "download_mccarthy = True              # Download McCarthy Buffer dataset\n", "verify_downloads = True               # Verify file integrity after download\n", "create_manifest = True                # Create dataset manifest file\n", "use_mlflow = True                     # Enable MLflow tracking"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import os\n", "import json\n", "import logging\n", "import subprocess\n", "from pathlib import Path\n", "from datetime import datetime\n", "import pandas as pd\n", "\n", "# MLflow for experiment tracking\n", "if use_mlflow:\n", "    import mlflow\n", "    import mlflow.sklearn\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"Data Acquisition: Point Cloud Download - Starting...\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup paths following project data organization\n", "project_root = Path('../..')  # Navigate to project root from notebooks/data_acquisition/\n", "data_path = project_root / 'data'\n", "raw_path = data_path / 'raw' / project_type / site_name\n", "output_path = data_path / 'output_runs'\n", "\n", "# Create directories\n", "raw_path.mkdir(parents=True, exist_ok=True)\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"Project root: {project_root.resolve()}\")\n", "print(f\"Raw data path: {raw_path.resolve()}\")\n", "print(f\"Output path: {output_path.resolve()}\")\n", "\n", "# Initialize MLflow if enabled\n", "if use_mlflow:\n", "    mlflow.set_experiment(f\"data_acquisition_{project_type}\")\n", "    mlflow.start_run(run_name=f\"download_{site_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}\")\n", "    mlflow.log_param(\"project_type\", project_type)\n", "    mlflow.log_param(\"site_name\", site_name)\n", "    mlflow.log_param(\"download_timestamp\", datetime.now().isoformat())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Dataset Configuration\n", "\n", "Define datasets for download with metadata:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Dataset configuration\n", "datasets = {\n", "    'castro_area4': {\n", "        'name': 'Castro Area4',\n", "        'remote_path': 'datasee-s3:preetam-filezilla-test/Castro/Pointcloud/area4_point.las',\n", "        'local_filename': 'castro_area4_point.las',\n", "        'expected_size_gb': 7.0,\n", "        'purpose': 'Primary dataset for method development',\n", "        'enabled': download_castro\n", "    },\n", "    'rpcs_point_cloud': {\n", "        'name': 'RPCS Point Cloud',\n", "        'remote_path': 'datasee-s3:preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/Point_Cloud.las',\n", "        'local_filename': 'rpcs_point_cloud.las',\n", "        'expected_size_gb': 1.3,\n", "        'purpose': 'Secondary dataset for validation',\n", "        'enabled': download_rpcs\n", "    },\n", "    'mccarthy_buffer': {\n", "        'name': '<PERSON>',\n", "        'remote_path': 'datasee-s3:preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/Buffer_las(rev1).las',\n", "        'local_filename': 'mccarthy_buffer.las',\n", "        'expected_size_gb': 0.036,\n", "        'purpose': 'Testing dataset for quick iterations',\n", "        'enabled': download_mccarthy\n", "    }\n", "}\n", "\n", "print(\"Dataset Configuration:\")\n", "for key, dataset in datasets.items():\n", "    status = \"ENABLED\" if dataset['enabled'] else \"DISABLED\"\n", "    print(f\"  {dataset['name']}: {status} ({dataset['expected_size_gb']} GB)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download Execution\n", "\n", "Download enabled datasets with progress tracking:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Download datasets\n", "download_results = []\n", "successful_downloads = []\n", "failed_downloads = []\n", "\n", "print(\"Starting dataset downloads...\")\n", "print(\"=\" * 50)\n", "\n", "for key, dataset in datasets.items():\n", "    if not dataset['enabled']:\n", "        print(f\"Skipping {dataset['name']} (disabled)\")\n", "        continue\n", "    \n", "    print(f\"\\nDownloading {dataset['name']}...\")\n", "    local_path = raw_path / dataset['local_filename']\n", "    \n", "    try:\n", "        # Execute rclone download\n", "        cmd = [\n", "            'rclone', 'copy',\n", "            dataset['remote_path'],\n", "            str(raw_path) + '/',\n", "            '--progress'\n", "        ]\n", "        \n", "        print(f\"Command: {' '.join(cmd)}\")\n", "        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)  # 1 hour timeout\n", "        \n", "        if result.returncode == 0:\n", "            # Rename file to standardized name\n", "            original_name = Path(dataset['remote_path']).name\n", "            original_path = raw_path / original_name\n", "            \n", "            if original_path.exists() and original_path != local_path:\n", "                original_path.rename(local_path)\n", "            \n", "            if local_path.exists():\n", "                actual_size_gb = local_path.stat().st_size / (1024**3)\n", "                print(f\"SUCCESS: {dataset['name']} downloaded ({actual_size_gb:.2f} GB)\")\n", "                successful_downloads.append(key)\n", "                \n", "                download_results.append({\n", "                    'dataset': dataset['name'],\n", "                    'status': 'SUCCESS',\n", "                    'expected_gb': dataset['expected_size_gb'],\n", "                    'actual_gb': actual_size_gb,\n", "                    'local_path': str(local_path),\n", "                    'purpose': dataset['purpose']\n", "                })\n", "            else:\n", "                raise FileNotFoundError(f\"Downloaded file not found: {local_path}\")\n", "        else:\n", "            raise subprocess.CalledProcessError(result.returncode, cmd, result.stderr)\n", "            \n", "    except Exception as e:\n", "        print(f\"FAILED: {dataset['name']} - {str(e)}\")\n", "        failed_downloads.append(key)\n", "        \n", "        download_results.append({\n", "            'dataset': dataset['name'],\n", "            'status': 'FAILED',\n", "            'expected_gb': dataset['expected_size_gb'],\n", "            'actual_gb': 0,\n", "            'local_path': '',\n", "            'purpose': dataset['purpose'],\n", "            'error': str(e)\n", "        })\n", "\n", "print(f\"\\nDownload Summary:\")\n", "print(f\"  Successful: {len(successful_downloads)}\")\n", "print(f\"  Failed: {len(failed_downloads)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download Verification and Analysis\n", "\n", "Verify downloads and generate comprehensive summary:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create download summary DataFrame\n", "if download_results:\n", "    summary_df = pd.DataFrame(download_results)\n", "    \n", "    print(\"DOWNLOAD SUMMARY:\")\n", "    print(\"=\" * 50)\n", "    print(summary_df[['dataset', 'status', 'expected_gb', 'actual_gb', 'purpose']].to_string(index=False))\n", "    \n", "    # Calculate totals\n", "    total_expected = summary_df['expected_gb'].sum()\n", "    total_actual = summary_df['actual_gb'].sum()\n", "    success_rate = len(successful_downloads) / len([d for d in datasets.values() if d['enabled']]) * 100\n", "    \n", "    print(f\"\\nSUMMARY STATISTICS:\")\n", "    print(f\"  Total expected size: {total_expected:.1f} GB\")\n", "    print(f\"  Total downloaded: {total_actual:.1f} GB\")\n", "    print(f\"  Success rate: {success_rate:.1f}%\")\n", "    print(f\"  Successful downloads: {len(successful_downloads)}\")\n", "    print(f\"  Failed downloads: {len(failed_downloads)}\")\n", "    \n", "    # Log metrics to MLflow\n", "    if use_mlflow:\n", "        mlflow.log_metric(\"total_size_gb\", total_actual)\n", "        mlflow.log_metric(\"success_rate\", success_rate)\n", "        mlflow.log_metric(\"datasets_downloaded\", len(successful_downloads))\n", "        mlflow.log_metric(\"datasets_failed\", len(failed_downloads))\n", "        \n", "        # Log dataset details\n", "        for result in download_results:\n", "            if result['status'] == 'SUCCESS':\n", "                mlflow.log_metric(f\"{result['dataset'].lower().replace(' ', '_')}_size_gb\", result['actual_gb'])\n", "else:\n", "    print(\"No download results to display\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Dataset Manifest Creation\n", "\n", "Create manifest file for downstream processing:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create dataset manifest for downstream processing\n", "if create_manifest and successful_downloads:\n", "    manifest = {\n", "        'project_type': project_type,\n", "        'site_name': site_name,\n", "        'download_timestamp': datetime.now().isoformat(),\n", "        'total_size_gb': sum(r['actual_gb'] for r in download_results if r['status'] == 'SUCCESS'),\n", "        'datasets': {}\n", "    }\n", "    \n", "    for key in successful_downloads:\n", "        dataset = datasets[key]\n", "        local_path = raw_path / dataset['local_filename']\n", "        \n", "        manifest['datasets'][key] = {\n", "            'name': dataset['name'],\n", "            'local_path': str(local_path),\n", "            'size_gb': local_path.stat().st_size / (1024**3) if local_path.exists() else 0,\n", "            'purpose': dataset['purpose'],\n", "            'expected_size_gb': dataset['expected_size_gb']\n", "        }\n", "    \n", "    # Save manifest\n", "    manifest_path = output_path / f\"{site_name}_data_acquisition_manifest_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json\"\n", "    with open(manifest_path, 'w') as f:\n", "        json.dump(manifest, f, indent=2)\n", "    \n", "    print(f\"\\nDataset manifest saved: {manifest_path}\")\n", "    \n", "    # Log manifest to MLflow\n", "    if use_mlflow:\n", "        mlflow.log_artifact(str(manifest_path), \"manifests\")\n", "        mlflow.log_param(\"manifest_path\", str(manifest_path))\n", "else:\n", "    print(\"\\nNo manifest created (disabled or no successful downloads)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Readiness Assessment\n", "\n", "Evaluate download success and readiness for analysis:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final readiness assessment\n", "print(\"\\nREADINESS ASSESSMENT:\")\n", "print(\"=\" * 50)\n", "\n", "if successful_downloads:\n", "    total_downloaded_size = sum(r['actual_gb'] for r in download_results if r['status'] == 'SUCCESS')\n", "    \n", "    print(f\"DOWNLOAD SUCCESS:\")\n", "    print(f\"  Datasets downloaded: {len(successful_downloads)}\")\n", "    print(f\"  Total size: {total_downloaded_size:.1f} GB\")\n", "    \n", "    # List successful downloads\n", "    for key in successful_downloads:\n", "        dataset = datasets[key]\n", "        local_path = raw_path / dataset['local_filename']\n", "        if local_path.exists():\n", "            actual_size_gb = local_path.stat().st_size / (1024**3)\n", "            print(f\"  - {dataset['name']}: {actual_size_gb:.2f} GB ({dataset['purpose']})\")\n", "    \n", "    # Readiness evaluation\n", "    if len(successful_downloads) >= 2:\n", "        print(f\"\\nSTATUS: READY FOR ANALYSIS\")\n", "        print(f\"  Multiple datasets available for comparative analysis\")\n", "        print(f\"  Sufficient data for method validation\")\n", "        \n", "        print(f\"\\nNEXT STEPS:\")\n", "        print(f\"  1. Begin preprocessing workflows\")\n", "        print(f\"  2. Implement analysis methods\")\n", "        print(f\"  3. Perform cross-validation\")\n", "        print(f\"  4. Generate comparative results\")\n", "        \n", "    elif len(successful_downloads) == 1:\n", "        print(f\"\\nSTATUS: PARTIAL SUCCESS\")\n", "        print(f\"  Single dataset available - can begin development\")\n", "        print(f\"  Limited cross-validation capability\")\n", "        print(f\"  Consider downloading additional datasets\")\n", "        \n", "else:\n", "    print(f\"STATUS: DOWNLOAD FAILED\")\n", "    print(f\"  No datasets successfully downloaded\")\n", "    print(f\"  Check network connectivity and credentials\")\n", "    print(f\"  Retry downloads or use alternative data sources\")\n", "\n", "# Finalize MLflow run\n", "if use_mlflow:\n", "    mlflow.log_param(\"final_status\", \"SUCCESS\" if successful_downloads else \"FAILED\")\n", "    mlflow.end_run()\n", "\n", "print(f\"\\nData acquisition completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "**Data Acquisition Results:**\n", "- Systematic download of foundation analysis datasets\n", "- Verification of data integrity and accessibility\n", "- Preparation for preprocessing and analysis workflows\n", "\n", "**Technical Implementation:**\n", "- Papermill-compatible parameterized execution\n", "- MLflow experiment tracking and metrics logging\n", "- Standardized data organization in data/raw directory\n", "- Comprehensive manifest generation for downstream processing\n", "\n", "**Next Phase:**\n", "- Proceed to preprocessing workflows (ground segmentation, filtering)\n", "- Begin method implementation and validation\n", "- Establish baseline performance metrics for comparative analysis"]}], "metadata": {"kernelspec": {"display_name": "pdf_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 4}